<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Asset Loading Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .asset-test {
            margin: 20px 0;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        .asset-test img {
            max-width: 200px;
            max-height: 200px;
            margin: 10px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>Hero Section Asset Loading Test</h1>
    
    <div class="asset-test">
        <h3>Pinterest Icon</h3>
        <img src="src/assets/Hero/Pinterest.svg" alt="Pinterest" onload="this.parentElement.classList.add('success')" onerror="this.parentElement.classList.add('error')">
    </div>
    
    <div class="asset-test">
        <h3>Instagram Icon</h3>
        <img src="src/assets/Hero/Instagram.svg" alt="Instagram" onload="this.parentElement.classList.add('success')" onerror="this.parentElement.classList.add('error')">
    </div>
    
    <div class="asset-test">
        <h3>LinkedIn Icon</h3>
        <img src="src/assets/Hero/LinkedIn.svg" alt="LinkedIn" onload="this.parentElement.classList.add('success')" onerror="this.parentElement.classList.add('error')">
    </div>
    
    <div class="asset-test">
        <h3>Dummy-01</h3>
        <img src="src/assets/Hero/Dummy-01.svg" alt="Dummy-01" onload="this.parentElement.classList.add('success')" onerror="this.parentElement.classList.add('error')">
    </div>
    
    <div class="asset-test">
        <h3>Dummy-02</h3>
        <img src="src/assets/Hero/Dummy-02.svg" alt="Dummy-02" onload="this.parentElement.classList.add('success')" onerror="this.parentElement.classList.add('error')">
    </div>
    
    <div class="asset-test">
        <h3>Dummy-03</h3>
        <img src="src/assets/Hero/Dummy-03.svg" alt="Dummy-03" onload="this.parentElement.classList.add('success')" onerror="this.parentElement.classList.add('error')">
    </div>
    
    <div class="asset-test">
        <h3>Dummy-04</h3>
        <img src="src/assets/Hero/Dummy-04.svg" alt="Dummy-04" onload="this.parentElement.classList.add('success')" onerror="this.parentElement.classList.add('error')">
    </div>
    
    <div class="asset-test">
        <h3>Dummy-05</h3>
        <img src="src/assets/Hero/Dummy-05.svg" alt="Dummy-05" onload="this.parentElement.classList.add('success')" onerror="this.parentElement.classList.add('error')">
    </div>

    <script>
        // Check if all images loaded after 3 seconds
        setTimeout(() => {
            const successCount = document.querySelectorAll('.success').length;
            const errorCount = document.querySelectorAll('.error').length;
            const totalCount = document.querySelectorAll('.asset-test').length;
            
            console.log(`Asset loading results: ${successCount} success, ${errorCount} errors, ${totalCount} total`);
            
            if (successCount === totalCount) {
                document.body.style.backgroundColor = '#d4edda';
                console.log('✅ All assets loaded successfully!');
            } else {
                document.body.style.backgroundColor = '#f8d7da';
                console.log('❌ Some assets failed to load');
            }
        }, 3000);
    </script>
</body>
</html>

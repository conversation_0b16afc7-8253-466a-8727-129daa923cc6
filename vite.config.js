import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    allowedHosts: ["mortality-foul-bon-apparel.trycloudflare.com"],
    // Enable HTTP/2 for better asset loading
    https: false,
    // Preload assets
    preTransformRequests: true,
  },
  build: {
    // Enable code splitting and chunk optimization
    rollupOptions: {
      output: {
        // Manual chunk splitting for better caching
        manualChunks: {
          // Vendor chunks
          "react-vendor": ["react", "react-dom"],
          "router-vendor": ["react-router-dom"],
          "ui-vendor": ["@mui/material", "@emotion/react", "@emotion/styled"],
          "animation-vendor": ["framer-motion"],
          "icons-vendor": ["@heroicons/react", "lucide-react", "react-icons"],
          "swiper-vendor": ["swiper"],

          // Feature chunks
          "home-components": [
            "./src/components/main_herosection/Main_section.jsx",
            "./src/components/ManagementFeatures/ManagementFeatures.jsx",
            "./src/components/social_media/SocialMediaIcons.jsx",
          ],
          "content-components": [
            "./src/components/Testimonials/Testimonials.jsx",
            "./src/components/InfoCard/InfoCard.jsx",
            "./src/components/AboutUs/AboutUsHero.jsx",
          ],
          "utility-components": [
            "./src/components/FAQ/FAQ.jsx",
            "./src/components/TextReveal/TextReveal.jsx",
            "./src/components/Thoughts/Thoughts.jsx",
          ],
        },

        // Optimize chunk file names with better caching
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId
            ? chunkInfo.facadeModuleId
                .split("/")
                .pop()
                .replace(".jsx", "")
                .replace(".js", "")
            : "chunk";
          return `js/${facadeModuleId}-[hash].js`;
        },
        entryFileNames: "js/[name]-[hash].js",
        assetFileNames: (assetInfo) => {
          const info = assetInfo.names?.[0] || assetInfo.name || "asset";
          const parts = info.split(".");
          const ext = parts[parts.length - 1];
          const name = parts.slice(0, -1).join(".");

          if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico|webp|avif)$/i.test(info)) {
            return `images/${name}-[hash].${ext}`;
          }
          if (/\.(css)$/i.test(info)) {
            return `css/${name}-[hash].${ext}`;
          }
          if (/\.(woff|woff2|eot|ttf|otf)$/i.test(info)) {
            return `fonts/${name}-[hash].${ext}`;
          }
          return `assets/${name}-[hash].${ext}`;
        },
      },
    },
    // Optimize chunk size warnings
    chunkSizeWarningLimit: 1000,
    // Enable source maps for debugging (disable in production)
    sourcemap: false,
    // Minify for production
    minify: "terser",
    terserOptions: {
      compress: {
        drop_console: true, // Remove console.log in production
        drop_debugger: true,
      },
    },
    // Asset optimization
    assetsInlineLimit: 4096, // Inline assets smaller than 4kb
    // Enable CSS code splitting
    cssCodeSplit: true,
  },
  // Optimize dependencies
  optimizeDeps: {
    include: [
      "react",
      "react-dom",
      "react-router-dom",
      "@mui/material",
      "framer-motion",
    ],
    // Force pre-bundling of these dependencies
    force: true,
  },
  // Asset processing optimizations
  assetsInclude: [
    "**/*.svg",
    "**/*.png",
    "**/*.jpg",
    "**/*.jpeg",
    "**/*.gif",
    "**/*.webp",
  ],
});

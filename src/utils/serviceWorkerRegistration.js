/**
 * Service Worker Registration and Management
 * Handles registration, updates, and communication with service worker
 */
import { useState, useEffect } from "react";

const isLocalhost = Boolean(
  window.location.hostname === "localhost" ||
    window.location.hostname === "[::1]" ||
    window.location.hostname.match(
      /^127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/
    )
);

/**
 * Register service worker
 */
export function register(config) {
  if ("serviceWorker" in navigator) {
    const publicUrl = import.meta.env.BASE_URL || "/";

    window.addEventListener("load", () => {
      const swUrl = `${publicUrl}sw.js`;

      if (isLocalhost) {
        checkValidServiceWorker(swUrl, config);
        navigator.serviceWorker.ready.then(() => {
          console.log("Service worker is ready for offline use.");
        });
      } else {
        registerValidSW(swUrl, config);
      }
    });
  }
}

/**
 * Register valid service worker
 */
function registerValidSW(swUrl, config) {
  navigator.serviceWorker
    .register(swUrl)
    .then((registration) => {
      console.log("Service Worker registered successfully:", registration);

      registration.onupdatefound = () => {
        const installingWorker = registration.installing;
        if (installingWorker == null) {
          return;
        }

        installingWorker.onstatechange = () => {
          if (installingWorker.state === "installed") {
            if (navigator.serviceWorker.controller) {
              console.log("New content is available; please refresh.");
              if (config && config.onUpdate) {
                config.onUpdate(registration);
              }
            } else {
              console.log("Content is cached for offline use.");
              if (config && config.onSuccess) {
                config.onSuccess(registration);
              }
            }
          }
        };
      };
    })
    .catch((error) => {
      console.error("Error during service worker registration:", error);
    });
}

/**
 * Check if service worker is valid
 */
function checkValidServiceWorker(swUrl, config) {
  fetch(swUrl, {
    headers: { "Service-Worker": "script" },
  })
    .then((response) => {
      const contentType = response.headers.get("content-type");
      if (
        response.status === 404 ||
        (contentType != null && contentType.indexOf("javascript") === -1)
      ) {
        navigator.serviceWorker.ready.then((registration) => {
          registration.unregister().then(() => {
            window.location.reload();
          });
        });
      } else {
        registerValidSW(swUrl, config);
      }
    })
    .catch(() => {
      console.log(
        "No internet connection found. App is running in offline mode."
      );
    });
}

/**
 * Unregister service worker
 */
export function unregister() {
  if ("serviceWorker" in navigator) {
    navigator.serviceWorker.ready
      .then((registration) => {
        registration.unregister();
      })
      .catch((error) => {
        console.error(error.message);
      });
  }
}

/**
 * Update service worker
 */
export function updateServiceWorker() {
  if ("serviceWorker" in navigator) {
    navigator.serviceWorker.ready.then((registration) => {
      registration.update();
    });
  }
}

/**
 * Send message to service worker
 */
export function sendMessageToSW(message) {
  if ("serviceWorker" in navigator && navigator.serviceWorker.controller) {
    navigator.serviceWorker.controller.postMessage(message);
  }
}

/**
 * Clear all caches via service worker
 */
export function clearCaches() {
  sendMessageToSW({ type: "CLEAR_CACHE" });
}

/**
 * Check if app is running offline
 */
export function isOffline() {
  return !navigator.onLine;
}

/**
 * Listen for online/offline events
 */
export function setupNetworkListeners(onOnline, onOffline) {
  window.addEventListener("online", () => {
    console.log("App is back online");
    if (onOnline) onOnline();
  });

  window.addEventListener("offline", () => {
    console.log("App is offline");
    if (onOffline) onOffline();
  });
}

/**
 * React hook for service worker management
 */

export function useServiceWorker() {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [updateAvailable, setUpdateAvailable] = useState(false);
  const [registration, setRegistration] = useState(null);

  useEffect(() => {
    // Register service worker
    register({
      onSuccess: (reg) => {
        console.log("Service Worker registered successfully");
        setRegistration(reg);
      },
      onUpdate: (reg) => {
        console.log("Service Worker update available");
        setUpdateAvailable(true);
        setRegistration(reg);
      },
    });

    // Setup network listeners
    setupNetworkListeners(
      () => setIsOnline(true),
      () => setIsOnline(false)
    );

    return () => {
      window.removeEventListener("online", () => setIsOnline(true));
      window.removeEventListener("offline", () => setIsOnline(false));
    };
  }, []);

  const applyUpdate = () => {
    if (registration && registration.waiting) {
      sendMessageToSW({ type: "SKIP_WAITING" });
      window.location.reload();
    }
  };

  return {
    isOnline,
    updateAvailable,
    applyUpdate,
    clearCaches,
    updateServiceWorker,
  };
}

/**
 * Cache management utilities
 */
export const cacheManager = {
  /**
   * Get cache storage usage
   */
  async getStorageUsage() {
    if ("storage" in navigator && "estimate" in navigator.storage) {
      const estimate = await navigator.storage.estimate();
      return {
        used: estimate.usage,
        available: estimate.quota,
        percentage: (estimate.usage / estimate.quota) * 100,
      };
    }
    return null;
  },

  /**
   * Clear specific cache
   */
  async clearCache(cacheName) {
    if ("caches" in window) {
      return await caches.delete(cacheName);
    }
    return false;
  },

  /**
   * Get all cache names
   */
  async getCacheNames() {
    if ("caches" in window) {
      return await caches.keys();
    }
    return [];
  },

  /**
   * Get cache size
   */
  async getCacheSize(cacheName) {
    if ("caches" in window) {
      const cache = await caches.open(cacheName);
      const keys = await cache.keys();
      return keys.length;
    }
    return 0;
  },
};

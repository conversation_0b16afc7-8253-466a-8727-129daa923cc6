import { useState, useEffect } from "react";
import youtubeIcon from "../../assets/svg_icon/youtube-icon.svg";
import redditIcon from "../../assets/svg_icon/reddit.svg";
import instagramIcon from "../../assets/svg_icon/instagram.svg";
import facebookIcon from "../../assets/svg_icon/facebook-icon.svg";
import twitterIcon from "../../assets/svg_icon/twitter-icon.svg";
import pinterestIcon from "../../assets/svg_icon/pintrest-icon.svg";
import linkedinIcon from "../../assets/svg_icon/linkdin-icon.svg";
import threadsIcon from "../../assets/svg_icon/thread.svg";
import Background from "../../assets/SocialMedia/Background.svg";
import Background_02 from "../../assets/SocialMedia/Background_02.svg";
import Background_03 from "../../assets/SocialMedia/Background_03.svg";
import Background_Shadow from "../../assets/SocialMedia/Background_Shadow.svg";

function SocialMediaIcons() {
  const [animationOffset, setAnimationOffset] = useState(0);
  const [roundCount, setRoundCount] = useState(0);
  const [isRestarting, setIsRestarting] = useState(false);

  const baseSocialIcons = [
    {
      icon: youtubeIcon,
      alt: "YouTube",
      platform: "youtube",
      color: "#FF0000",
    },
    {
      icon: redditIcon,
      alt: "Reddit",
      platform: "reddit",
      color: "#FF4500",
    },
    {
      icon: instagramIcon,
      alt: "Instagram",
      platform: "instagram",
      color: "#E4405F",
    },
    {
      icon: facebookIcon,
      alt: "Facebook",
      platform: "facebook",
      color: "#1877F2",
    },
    {
      icon: twitterIcon,
      alt: "X (Twitter)",
      platform: "twitter",
      color: "#000000",
    },
    {
      icon: pinterestIcon,
      alt: "Pinterest",
      platform: "pinterest",
      color: "#BD081C",
    },
    {
      icon: linkedinIcon,
      alt: "LinkedIn",
      platform: "linkedin",
      color: "#0A66C2",
    },
    {
      icon: threadsIcon,
      alt: "Threads",
      platform: "threads",
      color: "#000000",
    },
    {
      icon: redditIcon,
      alt: "Reddit",
      platform: "reddit",
      color: "#FF4500",
    },
    {
      icon: youtubeIcon,
      alt: "YouTube",
      platform: "youtube",
      color: "#FF0000",
    },
    {
      icon: facebookIcon,
      alt: "Facebook",
      platform: "facebook",
      color: "#1877F2",
    },
    {
      icon: instagramIcon,
      alt: "Instagram",
      platform: "instagram",
      color: "#E4405F",
    },
    {
      icon: threadsIcon,
      alt: "Threads",
      platform: "threads",
      color: "#000000",
    },
    {
      icon: pinterestIcon,
      alt: "Pinterest",
      platform: "pinterest",
      color: "#BD081C",
    },
    {
      icon: twitterIcon,
      alt: "X (Twitter)",
      platform: "twitter",
      color: "#000000",
    },
    {
      icon: linkedinIcon,
      alt: "LinkedIn",
      platform: "linkedin",
      color: "#0A66C2",
    },
    {
      icon: instagramIcon,
      alt: "Instagram",
      platform: "instagram",
      color: "#E4405F",
    },
    {
      icon: redditIcon,
      alt: "Reddit",
      platform: "reddit",
      color: "#FF4500",
    },
    {
      icon: pinterestIcon,
      alt: "Pinterest",
      platform: "pinterest",
      color: "#BD081C",
    },
    {
      icon: youtubeIcon,
      alt: "YouTube",
      platform: "youtube",
      color: "#FF0000",
    },
    {
      icon: linkedinIcon,
      alt: "LinkedIn",
      platform: "linkedin",
      color: "#0A66C2",
    },
    {
      icon: twitterIcon,
      alt: "X (Twitter)",
      platform: "twitter",
      color: "#000000",
    },
    {
      icon: facebookIcon,
      alt: "Facebook",
      platform: "facebook",
      color: "#1877F2",
    },
    {
      icon: threadsIcon,
      alt: "Threads",
      platform: "threads",
      color: "#000000",
    },
  ];

  const generateSocialIcons = (repeatCount) => {
    return Array(repeatCount)
      .fill(null)
      .flatMap(() => [...baseSocialIcons]);
  };

  const socialIcons = generateSocialIcons(15);

  // Responsive spacing and mobile state
  const [spacing, setSpacing] = useState(130);
  const [isMobile, setIsMobile] = useState(false);
  const centerPosition = Math.floor(socialIcons.length / 2);

  // Update spacing and mobile state on window resize
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth <= 640;
      const newSpacing = mobile ? 170 : window.innerWidth <= 768 ? 100 : 130;
      setSpacing(newSpacing);
      setIsMobile(mobile);
    };

    // Set initial state
    handleResize();

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const getIconPosition = (index) => {
    const basePosition = (index - centerPosition) * spacing;
    return basePosition - animationOffset;
  };

  const getIconScale = (index) => {
    const effectiveCenterIndex = centerPosition + animationOffset / spacing;

    const cycleLength = baseSocialIcons.length;
    const iconCyclePosition = index % cycleLength;
    const centerCyclePosition = effectiveCenterIndex % cycleLength;

    let distance = Math.abs(iconCyclePosition - centerCyclePosition);
    distance = Math.min(distance, cycleLength - distance);

    if (distance < 0.5) return 1; // Center icon
    if (distance < 1.5) return 0.8; // Adjacent icons
    if (distance < 2.5) return 0.7; // Second level
    return 0.6; // Far icons
  };

  const getIconOpacity = (index) => {
    const effectiveCenterIndex = centerPosition + animationOffset / spacing;

    const cycleLength = baseSocialIcons.length;
    const iconCyclePosition = index % cycleLength;
    const centerCyclePosition = effectiveCenterIndex % cycleLength;

    let distance = Math.abs(iconCyclePosition - centerCyclePosition);
    distance = Math.min(distance, cycleLength - distance);

    if (distance < 0.5) return 1; // Center icon
    if (distance <= 2.5) return 0.8; // Close icons
    if (distance <= 3.5) return 0.8; // Medium distance
    return 0.4; // Far icons
  };

  useEffect(() => {
    const interval = setInterval(() => {
      setAnimationOffset((prev) => {
        const cycleLength = baseSocialIcons.length * spacing;
        const newOffset = prev + spacing;

        if (newOffset >= cycleLength) {
          setRoundCount((prevRound) => {
            const newRoundCount = prevRound + 1;

            if (newRoundCount >= 15) {
              setIsRestarting(true);

              setTimeout(() => {
                setAnimationOffset(0);
                setRoundCount(0);
                setIsRestarting(false);
              }, 1000); // 1 second pause before restarting

              return 0; // Reset round count
            }

            return newRoundCount;
          });

          return 0; // Reset offset for next cycle
        }

        return newOffset;
      });
    }, 2000); // Increased to 2.5 seconds for smoother performance

    return () => clearInterval(interval);
  }, [spacing]);

  return (
    <section className="w-full py-36 px-4 bg-[#FFFFFF] relative overflow-hidden flex items-center">
      {/* Main content */}
      <div className="mx-auto text-center relative z-10 w-full -mt-5">
        {/* Title */}
        <h2 className="text-[30px] md:text-[38px] lg:text-[43px] xl:text-[43px] font-medium text-[#563D39] mb-6 leading-tight">
          Streamline Your Social Media
          <br />
          With Flowkar
        </h2>

        {/* Subtitle */}
        <p className="min-h-[10vh] text-[16px] md:text-[20px] lg:text-[24px] text-[#00000099] max-w-4xl mx-auto font-light mb-[78px]">
          One tool to schedule, track, and grow your socials.
        </p>

        {/* Social Media Icons Container */}
        <div className="relative">
          {/* Background Images */}
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <img
              src={Background_Shadow}
              alt="Shadow"
              className="absolute w-[583px] h-[500px]"
              loading="lazy"
              decoding="async"
              width={583}
              height={500}
            />
            <img
              src={Background_03}
              alt=""
              className="absolute w-[583px] h-[500px]"
              loading="lazy"
              decoding="async"
              width={583}
              height={500}
            />
          </div>

          {/* Social Media Icons Container with overflow hidden for smooth sliding */}
          <div
            className="relative overflow-hidden"
            style={{
              height: isMobile ? "120px" : "150px", // Adjust height for mobile
            }}
          >
            <div
              className={`absolute inset-0 flex items-center justify-center transition-opacity duration-1000 ${
                isRestarting ? "opacity-30" : "opacity-100"
              }`}
            >
              {socialIcons.map((social, index) => {
                const scale = getIconScale(index);
                const opacity = getIconOpacity(index);
                const translateX = getIconPosition(index);

                // Determine if this icon is currently highlighted (closest to center)
                // Use the same logic as getIconScale and getIconOpacity for consistency
                const effectiveCenterIndex =
                  centerPosition + animationOffset / spacing;
                const cycleLength = baseSocialIcons.length;
                const iconCyclePosition = index % cycleLength;
                const centerCyclePosition = effectiveCenterIndex % cycleLength;

                // Calculate minimum distance considering wrap-around (same as in getIconScale)
                let distance = Math.abs(
                  iconCyclePosition - centerCyclePosition
                );
                distance = Math.min(distance, cycleLength - distance);
                const isHighlighted = distance < 0.5;

                return (
                  <div
                    key={`${social.platform}-${index}`}
                    className="absolute flex items-center justify-center rounded-[9px] transition-all duration-1000 ease-in-out"
                    style={{
                      // Remove scale transform on mobile to prevent squeezing
                      transform: isMobile
                        ? `translateX(${translateX}px)`
                        : `translateX(${translateX}px) scale(${scale})`,
                      opacity: opacity,
                      zIndex: isHighlighted ? 20 : 10 - Math.floor(distance),
                    }}
                  >
                    {/* Corner brackets */}
                    {/* Top Left */}
                    <span
                      className="absolute -top-3 -left-6 md:-left-3 w-6 h-6 border-t-[2px] border-l-[2px] z-20 rounded-tl-[10px] transition-colors duration-1000"
                      style={{
                        borderColor: isHighlighted ? social.color : "#d1d5db",
                      }}
                    />

                    {/* Top Right */}
                    <span
                      className="absolute -top-3 -right-6 md:-right-3 w-6 h-6 border-t-[2px] border-r-[2px] z-20 rounded-tr-[10px] transition-colors duration-1000"
                      style={{
                        borderColor: isHighlighted ? social.color : "#d1d5db",
                      }}
                    />

                    {/* Bottom Left */}
                    <span
                      className="absolute -bottom-3 -left-6 md:-left-3 w-6 h-6 border-b-[2px] border-l-[2px] z-20 rounded-bl-[10px] transition-colors duration-1000"
                      style={{
                        borderColor: isHighlighted ? social.color : "#d1d5db",
                      }}
                    />

                    {/* Bottom Right */}
                    <span
                      className="absolute -bottom-3 -right-6 md:-right-3 w-6 h-6 border-b-[2px] border-r-[2px] z-20 rounded-br-[10px] transition-colors duration-1000"
                      style={{
                        borderColor: isHighlighted ? social.color : "#d1d5db",
                      }}
                    />

                    {/* Icon Container */}
                    <div
                      className="relative flex items-center justify-center rounded-[9px] transition-all duration-1000 ease-in-out"
                      style={{
                        backgroundColor: isHighlighted
                          ? `${social.color}10`
                          : "#f8f9fa",
                        // Fixed dimensions to prevent squeezing - using state instead of window.innerWidth
                        width: isHighlighted
                          ? isMobile
                            ? "72px"
                            : "96px"
                          : isMobile
                          ? "56px"
                          : "62px",
                        height: isHighlighted
                          ? isMobile
                            ? "72px"
                            : "96px"
                          : isMobile
                          ? "56px"
                          : "62px",
                        boxShadow: isHighlighted
                          ? "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
                          : "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
                        // Ensure consistent width during animation
                        minWidth: isHighlighted
                          ? isMobile
                            ? "72px"
                            : "96px"
                          : isMobile
                          ? "56px"
                          : "62px",
                        minHeight: isHighlighted
                          ? isMobile
                            ? "72px"
                            : "96px"
                          : isMobile
                          ? "56px"
                          : "62px",
                        maxWidth: isHighlighted
                          ? isMobile
                            ? "72px"
                            : "96px"
                          : isMobile
                          ? "56px"
                          : "62px",
                        maxHeight: isHighlighted
                          ? isMobile
                            ? "72px"
                            : "96px"
                          : isMobile
                          ? "56px"
                          : "62px",
                        flexShrink: 0, // Prevent shrinking during animation
                      }}
                    >
                      {/* Icon */}
                      <img
                        src={social.icon}
                        alt={social.alt}
                        className="object-contain relative z-10 transition-all duration-1000"
                        style={{
                          // Fixed icon sizes to prevent squeezing - using state instead of window.innerWidth
                          width: isHighlighted
                            ? isMobile
                              ? "48px"
                              : "64px"
                            : isMobile
                            ? "32px"
                            : "40px",
                          height: isHighlighted
                            ? isMobile
                              ? "48px"
                              : "64px"
                            : isMobile
                            ? "32px"
                            : "40px",
                          // Prevent any scaling or distortion
                          minWidth: isHighlighted
                            ? isMobile
                              ? "48px"
                              : "64px"
                            : isMobile
                            ? "32px"
                            : "40px",
                          minHeight: isHighlighted
                            ? isMobile
                              ? "48px"
                              : "64px"
                            : isMobile
                            ? "32px"
                            : "40px",
                          maxWidth: isHighlighted
                            ? isMobile
                              ? "48px"
                              : "64px"
                            : isMobile
                            ? "32px"
                            : "40px",
                          maxHeight: isHighlighted
                            ? isMobile
                              ? "48px"
                              : "64px"
                            : isMobile
                            ? "32px"
                            : "40px",
                          flexShrink: 0,
                        }}
                        loading="lazy"
                        decoding="async"
                        width={
                          isHighlighted
                            ? isMobile
                              ? 48
                              : 64
                            : isMobile
                            ? 32
                            : 40
                        }
                        height={
                          isHighlighted
                            ? isMobile
                              ? 48
                              : 64
                            : isMobile
                            ? 32
                            : 40
                        }
                      />
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default SocialMediaIcons;

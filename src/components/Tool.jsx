import React from "react";
import youtube from "../assets/youtube.svg";
import linkedin from "../assets/linkedin.svg";
import instagram from "../assets/instagram.svg";
import pintrest from "../assets/pintrest.svg";
import vimeo from "../assets/vimeo.png";
import thread from "../assets/thread.svg";
import facebook from "../assets/facebook.svg";
import radit from "../assets/raddit.svg";

const Tool = () => {
    return (
        <div className="w-full overflow-hidden bg-white py-4 mt-10 mb-10">
            <div className="relative flex w-full animate-scroll gap-6">
                {/* Container for scrolling logos */}
                {[
                    youtube,
                    linkedin,
                    instagram,
                    radit,
                    pintrest,
                    vimeo,
                    facebook,
                    thread,
                ].map((icon, index) => (
                    <div
                        key={index}
                        className="flex-shrink-0 flex justify-center items-center w-[33.33%] sm:w-[25%] md:w-[20%] lg:w-[16.66%]"
                    >
                        <img
                            src={icon}
                            alt={`Icon ${index}`}
                            className="w-[4rem] h-auto sm:w-[5rem] md:w-[6rem] lg:w-[7rem] xl:w-[8rem] object-contain max-h-[80px]"
                        />
                    </div>
                ))}
            </div>
        </div>
    );
};

export default Tool;

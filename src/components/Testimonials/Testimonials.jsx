import { useState, useEffect, useRef } from "react";
import Profile from "../../assets/svg_icon/Profile.jpg";
import UserProfile from "../../assets/svg_icon/User_profile.svg";
import DummyProfile from "../../assets/svg_icon/dummy_profile.svg";
import Girl_01 from "../../assets/Testimonials/Girl_01.png";
import Girl_02 from "../../assets/Testimonials/Girl_02.png";
import Girl_03 from "../../assets/Testimonials/Girl_03.png";
import Girl_04 from "../../assets/Testimonials/Girl_04.png";
import Boy_01 from "../../assets/Testimonials/Boy_01.png";
import Boy_02 from "../../assets/Testimonials/Boy_02.png";
import Boy_03 from "../../assets/Testimonials/Boy_03.png";
import Boy_04 from "../../assets/Testimonials/Boy_04.png";

const Testimonials = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  const [touchStart, setTouchStart] = useState(null);
  const [touchEnd, setTouchEnd] = useState(null);
  const sliderRef = useRef(null);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  const testimonials = [
    {
      name: " Riya Patel",
      role: " Content Strategist",
      image: Girl_01,
      content:
        "Flowkar completely changed how I manage my brand's social media. The UI is so intuitive, I scheduled a month’s worth of content in just one evening!",
    },
    {
      name: "Siddharth Mehta",
      role: "Agency Founder",
      image: Boy_01,
      content:
        "We used to juggle 4 different tools just to stay active online. With Flowkar, our entire team collaborates, schedules, and tracks performance all in one place.",
    },
    {
      name: "Tanya Rawal",
      role: "Digital Creator",
      image: Girl_02,
      content:
        "The ability to post across Instagram, LinkedIn, Facebook, and even YouTube Shorts with one click is a game-changer. Total time-saver!",
    },
    {
      name: "Amit Bhansali",
      role: " Social Media Intern",
      image: Boy_02,
      content:
        "I love how Flowkar simplifies everything. Drafting content, adding hashtags, and scheduling it with smart recommendations is just... effortless.",
    },
    {
      name: "Neha Joshi",
      role: "Product Marketer",
      image: Girl_03,
      content:
        "Finally, a platform that’s not only powerful but looks good too. The interface is smooth, modern, and super fast.",
    },
    {
      name: "Arjun Desai",
      role: " Fitness Influencer",
      image: Boy_03,
      content:
        "Flowkar’s analytics helped me identify the best time to post for engagement. I saw a 60% boost in reach within a week!",
    },
    {
      name: " Rahul Nair",
      role: " Startup Founder",
      image: Boy_04,
      content:
        "As a startup founder, I needed speed and clarity. Flowkar gave me both — powerful features without the learning curve.",
    },
    {
      name: " Mitali Kapoor",
      role: " Campaign Manager",
      image: Girl_04,
      content:
        "Flowkar made launching our campaign across multiple platforms feel like a breeze. Seamless, smart, and stress-free!",
    },
  ];

  const nextSlide = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
    );
  };

  const prevSlide = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1
    );
  };

  // Touch event handlers for swipe functionality
  const handleTouchStart = (e) => {
    setTouchEnd(null); // Reset touchEnd
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 0;
    const isRightSwipe = distance < 0;

    if (isLeftSwipe && isMobile) {
      nextSlide();
    }
    if (isRightSwipe && isMobile) {
      prevSlide();
    }
  };

  // Create extended array for infinite effect
  const extendedTestimonials = [
    ...testimonials.slice(-2), // Last 2 items at the beginning
    ...testimonials,
    ...testimonials.slice(0, 2), // First 2 items at the end
  ];

  // Calculate responsive dimensions
  const getCardWidth = () => (isMobile ? 280 : 440);
  const getGap = () => (isMobile ? 16 : 32);
  const getTotalWidth = () => getCardWidth() + getGap();

  return (
    <div className="bg-white dark:bg-darkBlack lg:pt-20">
      <div className="mx-auto px-4 py-12">
        <div className="text-center">
          {/* Header */}
          <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-semibold text-[#563D39] dark:text-white mb-8 md:mb-16">
            What Our Client Said
          </h1>

          {/* Testimonials Container */}
          <div className="relative mx-auto overflow-hidden p-2 md:p-5">
            {/* Testimonials Slider */}
            <div className="relative">
              <div
                ref={sliderRef}
                className="flex transition-transform duration-500 ease-in-out gap-4 md:gap-8"
                style={{
                  transform: `translateX(-${
                    (currentIndex + 2) * getTotalWidth() - getTotalWidth()
                  }px)`,
                  width: `${extendedTestimonials.length * getTotalWidth()}px`,
                }}
                onTouchStart={handleTouchStart}
                onTouchMove={handleTouchMove}
                onTouchEnd={handleTouchEnd}
              >
                {extendedTestimonials.map((testimonial, index) => {
                  return (
                    <div
                      key={`${testimonial.name}-${index}`}
                      className={`flex-shrink-0 ${
                        isMobile ? "w-[280px]" : "w-[440px]"
                      } rounded-[20px] p-4 md:p-8 shadow-lg transition-all duration-300 ease-out border bg-white dark:bg-darkGrayCard border-gray-200 dark:border-gray-700 text-black dark:text-white md:hover:bg-[#563D39]  md:hover:border-[#563D39]  md:hover:text-white  md:hover:scale-[1.02]  group cursor-pointer transform-gpu overflow-y-auto`}
                    >
                      {/* Profile Section */}
                      <div className="flex items-center mb-4 md:mb-6">
                        <div className="w-10 h-10 md:w-14 md:h-14 rounded-full overflow-hidden mr-3 md:mr-4 flex-shrink-0">
                          <img
                            src={testimonial.image}
                            alt={testimonial.name}
                            className="w-full h-full object-cover"
                            loading="lazy"
                            decoding="async"
                          />
                        </div>
                        <div className="text-left">
                          <h3 className="font-semibold text-sm md:text-lg text-black dark:text-white md:group-hover:text-white transition-colors duration-300 ease-out">
                            {testimonial.name}
                          </h3>
                          {/* <p className="text-xs md:text-sm text-gray-600 dark:text-gray-400 md:group-hover:text-gray-200 transition-colors duration-300 ease-out">
                            {testimonial.role}
                          </p> */}
                        </div>
                      </div>

                      {/* Testimonial Content */}
                      <p className="text-left text-sm md:text-base leading-relaxed text-gray-700 dark:text-gray-300 md:group-hover:text-gray-100 transition-colors duration-300 ease-out">
                        {testimonial.content}
                      </p>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Navigation Arrows */}
            <div className="flex justify-center items-center space-x-3 md:space-x-4 mt-6 md:mt-8">
              <button
                onClick={prevSlide}
                className="w-10 h-10 md:w-12 md:h-12 rounded-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 transition-all duration-200 ease-out flex items-center justify-center transform hover:scale-105 active:scale-95"
              >
                <svg
                  className="w-5 h-5 md:w-6 md:h-6 text-gray-600 dark:text-gray-300 transition-transform duration-200 ease-out"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
              </button>

              <button
                onClick={nextSlide}
                className="w-10 h-10 md:w-12 md:h-12 rounded-full bg-[#563D39] hover:bg-[#6B4A45] transition-all duration-200 ease-out flex items-center justify-center transform hover:scale-105 active:scale-95"
              >
                <svg
                  className="w-5 h-5 md:w-6 md:h-6 text-white transition-transform duration-200 ease-out"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Testimonials;

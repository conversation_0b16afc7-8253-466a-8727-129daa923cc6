import React, { useState, useEffect, useRef, memo } from "react";
import assetPreloader from "../../utils/assetPreloader";

/**
 * OptimizedImage Component
 * Provides intelligent image loading with caching, lazy loading, and performance optimizations
 */
const OptimizedImage = memo(
  ({
    src,
    alt = "",
    className = "",
    width,
    height,
    priority = false,
    lazy = true,
    placeholder = null,
    onLoad,
    onError,
    style = {},
    ...props
  }) => {
    const [isLoaded, setIsLoaded] = useState(false);
    const [isError, setIsError] = useState(false);
    const [imageSrc, setImageSrc] = useState(placeholder);
    const imgRef = useRef(null);
    const observerRef = useRef(null);

    // Intersection Observer for lazy loading
    useEffect(() => {
      if (!lazy || priority) {
        loadImage();
        return;
      }

      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              loadImage();
              observer.unobserve(entry.target);
            }
          });
        },
        {
          rootMargin: "50px", // Start loading 50px before the image enters viewport
          threshold: 0.1,
        }
      );

      if (imgRef.current) {
        observer.observe(imgRef.current);
        observerRef.current = observer;
      }

      return () => {
        if (observerRef.current) {
          observerRef.current.disconnect();
        }
      };
    }, [src, lazy, priority]);

    const loadImage = async () => {
      try {
        console.log(`Attempting to load image: ${src}`);
        const cachedAsset = await assetPreloader.getAsset(src);
        console.log(`Successfully loaded image: ${src}`, cachedAsset);
        setImageSrc(src);
        setIsLoaded(true);
        onLoad?.(cachedAsset);
      } catch (error) {
        console.error(`Failed to load image: ${src}`, error);
        setIsError(true);
        onError?.(error);
      }
    };

    // Preload critical images immediately
    useEffect(() => {
      if (priority) {
        assetPreloader.preloadAsset(src, true);
      }
    }, [src, priority]);

    const imageStyle = {
      ...style,
      transition: "opacity 0.3s ease-in-out",
      opacity: isLoaded ? 1 : 0.7,
    };

    if (isError) {
      return (
        <div
          className={`bg-gray-200 flex items-center justify-center ${className}`}
          style={{ width, height, ...style }}
          {...props}
        >
          <span className="text-gray-500 text-sm">Failed to load image</span>
        </div>
      );
    }

    return (
      <img
        ref={imgRef}
        src={imageSrc}
        alt={alt}
        className={className}
        width={width}
        height={height}
        style={imageStyle}
        loading={priority ? "eager" : "lazy"}
        decoding="async"
        fetchPriority={priority ? "high" : "low"}
        {...props}
      />
    );
  }
);

OptimizedImage.displayName = "OptimizedImage";

export default OptimizedImage;

/**
 * Progressive Image Component
 * Loads a low-quality placeholder first, then the full image
 */
export const ProgressiveImage = memo(
  ({ src, placeholderSrc, alt = "", className = "", ...props }) => {
    const [currentSrc, setCurrentSrc] = useState(placeholderSrc || src);
    const [isLoaded, setIsLoaded] = useState(false);

    useEffect(() => {
      if (placeholderSrc) {
        // Load placeholder first
        assetPreloader.preloadAsset(placeholderSrc, true).then(() => {
          setCurrentSrc(placeholderSrc);
          // Then load full image
          assetPreloader.preloadAsset(src, false).then(() => {
            setCurrentSrc(src);
            setIsLoaded(true);
          });
        });
      } else {
        assetPreloader.preloadAsset(src, false).then(() => {
          setCurrentSrc(src);
          setIsLoaded(true);
        });
      }
    }, [src, placeholderSrc]);

    return (
      <OptimizedImage
        src={currentSrc}
        alt={alt}
        className={`${className} ${isLoaded ? "opacity-100" : "opacity-70"}`}
        style={{
          filter: isLoaded ? "none" : "blur(2px)",
          transition: "filter 0.3s ease-in-out, opacity 0.3s ease-in-out",
        }}
        {...props}
      />
    );
  }
);

ProgressiveImage.displayName = "ProgressiveImage";

/**
 * Image Grid Component with optimized loading
 */
export const OptimizedImageGrid = memo(
  ({
    images,
    className = "",
    itemClassName = "",
    priority = false,
    batchSize = 4,
  }) => {
    const [loadedBatches, setLoadedBatches] = useState(1);
    const gridRef = useRef(null);

    useEffect(() => {
      // Preload first batch immediately
      const firstBatch = images.slice(0, batchSize);
      const firstBatchUrls = firstBatch.map((img) => img.src || img);

      if (priority) {
        assetPreloader.preloadCriticalAssets(firstBatchUrls);
      } else {
        assetPreloader.batchPreload(firstBatchUrls, { priority: false });
      }

      // Preload remaining batches on idle
      const remainingImages = images
        .slice(batchSize)
        .map((img) => img.src || img);
      if (remainingImages.length > 0) {
        assetPreloader.preloadOnIdle(remainingImages);
      }
    }, [images, batchSize, priority]);

    const handleLoadMore = () => {
      setLoadedBatches((prev) =>
        Math.min(prev + 1, Math.ceil(images.length / batchSize))
      );
    };

    const visibleImages = images.slice(0, loadedBatches * batchSize);

    return (
      <div ref={gridRef} className={className}>
        {visibleImages.map((image, index) => {
          const imgSrc = typeof image === "string" ? image : image.src;
          const imgAlt =
            typeof image === "string" ? `Image ${index + 1}` : image.alt;
          const imgProps = typeof image === "object" ? image : {};

          return (
            <OptimizedImage
              key={imgSrc}
              src={imgSrc}
              alt={imgAlt}
              className={itemClassName}
              priority={index < 4} // First 4 images are priority
              {...imgProps}
            />
          );
        })}

        {loadedBatches * batchSize < images.length && (
          <button
            onClick={handleLoadMore}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            Load More Images
          </button>
        )}
      </div>
    );
  }
);

OptimizedImageGrid.displayName = "OptimizedImageGrid";

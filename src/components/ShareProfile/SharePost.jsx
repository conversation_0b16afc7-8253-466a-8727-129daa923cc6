import React, { useEffect, useState, Suspense, lazy } from "react";
import { useLocation } from "react-router-dom";
import apiInstance from "../../Axios/axiosInstance";
import flowkar from "../../assets/svg_icon/Flowkar.svg";
import { LucideDot, MoreHorizontal, Loader2 } from "lucide-react";
import Link_icon_Active from "../../assets/svg_icon/Like_Icon_Full.svg";
import Link_icon from "../../assets/svg_icon/Like_icon.svg";
import Save_icon from "../../assets/svg_icon/Save_icon.svg";
import Save_icon_Active from "../../assets/svg_icon/Save_icon_Full.svg";
import Comment_icon from "../../assets/svg_icon/Comment_Icon.svg";
import Share_icon from "../../assets/svg_icon/Share_Icon.svg";
import Emonji_Icon from "../../assets/svg_icon/Emoji_Icon.svg";
import ProfileImage from "./ProfileImage"; // Import the new component
import NoData from "./NoData";
import instagramIcon from "../../assets/svg_icon/instagram.svg";
import linkedinIcon from "../../assets/svg_icon/linkdin-icon.svg";
import pinterestIcon from "../../assets/svg_icon/pintrest-icon.svg";
import facebookIcon from "../../assets/svg_icon/facebook-icon.svg";
import tiktokIcon from "../../assets/svg_icon/tiktok-icon.svg";
import vimeoIcon from "../../assets/svg_icon/vimeo-icon.svg";
import youtubeIcon from "../../assets/svg_icon/youtube-icon.svg";
import threadIcon from "../../assets/svg_icon/thread.svg";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination } from "swiper/modules";

const platformToIcon = {
  Instagram: instagramIcon,
  Linkedin: linkedinIcon,
  Pinterest: pinterestIcon,
  Facebook: facebookIcon,
  TikTok: tiktokIcon,
  Vimeo: vimeoIcon,
  YouTube: youtubeIcon,
  Threads: threadIcon,
};

// Create a separate component for the media content
const MediaContent = ({ postFiles, fileTypes }) => {
  // Dynamically import CSS when component mounts
  useEffect(() => {
    import("swiper/css");
    import("swiper/css/navigation");
    import("swiper/css/pagination");
  }, []);

  const isVideoFile = (fileUrl, index) => {
    // Check file_types array first if available
    if (fileTypes && fileTypes[index]) {
      return fileTypes[index].toLowerCase() === "video";
    }

    // Fallback to checking file extension
    const extension = fileUrl.split(".").pop().toLowerCase();
    return ["mp4", "webm", "ogg", "mov"].includes(extension);
  };

  // Media renderer component that handles both images and videos
  const MediaRenderer = ({ fileUrl, index }) => {
    const isVideo = isVideoFile(fileUrl, index);

    if (isVideo) {
      return (
        <div className="relative w-full h-full">
          <video
            src={fileUrl}
            className="w-full h-full max-h-[400px] rounded-lg object-cover cursor-pointer"
            controls
            disablePictureInPicture
            controlsList="nodownload nofullscreen noremoteplayback"
            onClick={(e) => {
              if (e.target.paused) {
                e.target.play();
              } else {
                e.target.pause();
              }
            }}
          />
        </div>
      );
    } else {
      return (
        <img
          src={fileUrl}
          alt={`Media ${index + 1}`}
          className="w-full h-full max-h-[400px] rounded-lg object-cover"
          loading="lazy"
        />
      );
    }
  };

  return (
    <Swiper
      modules={[Navigation, Pagination]}
      navigation
      pagination={{ clickable: true }}
      className="w-full rounded-lg custom-swiper aspect-square md:aspect-auto"
    >
      {postFiles.map((file, index) => (
        <SwiperSlide key={index}>
          <MediaRenderer fileUrl={file} index={index} />
        </SwiperSlide>
      ))}
    </Swiper>
  );
};

// Loading skeleton component
const LoadingSkeleton = () => (
  <div className="w-full h-full flex flex-col space-y-4 animate-pulse">
    <div className="flex items-center space-x-3">
      <div className="rounded-full bg-gray-200 h-12 w-12"></div>
      <div className="flex flex-col space-y-2">
        <div className="h-4 bg-gray-200 rounded w-24"></div>
        <div className="h-3 bg-gray-200 rounded w-32"></div>
      </div>
    </div>
    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
    <div className="h-48 bg-gray-200 rounded w-full"></div>
  </div>
);

// Comments skeleton
const CommentsSkeleton = () => (
  <div className="w-full flex flex-col space-y-4 animate-pulse">
    {[1, 2, 3].map((idx) => (
      <div key={idx} className="flex space-x-3">
        <div className="rounded-full bg-gray-200 h-10 w-10"></div>
        <div className="flex flex-col space-y-2 flex-1">
          <div className="h-3 bg-gray-200 rounded w-full"></div>
          <div className="h-3 bg-gray-200 rounded w-3/4"></div>
        </div>
      </div>
    ))}
  </div>
);

function SharePost() {
  const location = useLocation();
  const [singlePost, setSinglePost] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);
  const [noData, setNoData] = useState(false);
  const searchParams = new URLSearchParams(location.search);
  const post_id = searchParams.get("post_id");
  const [isLiked, setIsLiked] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [isFollowing, setIsFollowing] = useState(false);

  // Function to format date difference
  const formatTimeDiff = (dateString) => {
    if (!dateString) return "";

    const postDate = new Date(dateString);
    const currentDate = new Date();

    // Calculate time differences in various units
    const diffInMs = currentDate - postDate;
    const diffInSecs = Math.floor(diffInMs / 1000);
    const diffInMins = Math.floor(diffInSecs / 60);
    const diffInHours = Math.floor(diffInMins / 60);
    const diffInDays = Math.floor(diffInHours / 24);
    const diffInWeeks = Math.floor(diffInDays / 7);
    const diffInMonths = Math.floor(diffInDays / 30);
    const diffInYears = Math.floor(diffInDays / 365);

    // Return appropriate format based on time difference
    if (diffInSecs < 60) return `${diffInSecs}s`;
    if (diffInMins < 60) return `${diffInMins}m`;
    if (diffInHours < 24) return `${diffInHours}h`;
    if (diffInDays < 7) return `${diffInDays}d`;
    if (diffInWeeks < 4) return `${diffInWeeks}w`;
    if (diffInMonths < 12) return `${diffInMonths}mo`;
    return `${diffInYears}y`;
  };

  const handleLike = () => {
    setIsLiked(!isLiked);
  };

  useEffect(() => {
    if (post_id) {
      console.log(post_id);

      const fetchSinglePost = async () => {
        try {
          const { data } = await apiInstance.get(`/get-post-web/`, {
            params: {
              post_id: post_id,
            },
          });

          console.log("API Response Data:", data);

          if (
            data &&
            data.results &&
            data.results.data &&
            data.results.data.length > 0
          ) {
            setSinglePost(data);
            setNoData(false);
          } else {
            setNoData(true);
          }

          setTimeout(() => setLoading(false), 300);
        } catch (error) {
          console.error("API Error:", error);
          setError("Error fetching, Please try again.");
          setNoData(true);
          setLoading(false);
        }
      };

      fetchSinglePost();
    } else {
      setNoData(true);
      setLoading(false);
    }
  }, [post_id]);

  const commentsData = singlePost?.results?.data?.[0]?.comments || [];
  const postFiles = singlePost?.results?.data?.[0]?.files || [];
  const fileTypes = singlePost?.results?.data?.[0]?.file_types || [];
  const createdAt = singlePost?.results?.data?.[0]?.created_at || "";
  const userName = singlePost?.results?.data?.[0]?.user?.name || "";
  const socialLinks = singlePost?.results?.data?.[0]?.social_links || [];

  const handleSave = () => {
    setIsSaved(!isSaved);
  };

  const handleFollow = () => {
    setIsFollowing(!isFollowing);
  };

  const handleRedirect = () => {
    const userdataString = localStorage.getItem("UserId");

    if (userdataString) {
      try {
        const userdata = JSON.parse(userdataString);
        // Check if userdata exists and has required properties
        if (userdata) {
          window.location.href = "https://app.flowkar.com/dashboard";
        } else {
          window.location.href = "https://app.flowkar.com/sign-in";
        }
      } catch (error) {
        console.error("Error parsing user data:", error);
        window.location.href = "https://app.flowkar.com/sign-in";
      }
    } else {
      window.location.href = "https://app.flowkar.com/sign-in";
    }
  };

  const handleRedirectSignUp = () => {
    const userdataString = localStorage.getItem("UserId");

    if (userdataString) {
      try {
        const userdata = JSON.parse(userdataString);
        // Check if userdata exists and has required properties
        if (userdata) {
          window.location.href = "https://app.flowkar.com/dashboard";
        } else {
          window.location.href = "https://app.flowkar.com/sign-up";
        }
      } catch (error) {
        console.error("Error parsing user data:", error);
        window.location.href = "https://app.flowkar.com/sign-up";
      }
    } else {
      window.location.href = "https://app.flowkar.com/sign-up";
    }
  };

  return (
    <div className="font-Ubuntu min-h-screen bg-[#F9F9F9] w-full overflow-hidden overflow-y-auto">
      {/* Header */}
      <div className="bg-white shadow-md py-2 md:py-3 sticky top-0 z-10">
        <div className="w-11/12 max-w-7xl mx-auto flex justify-between items-center">
          <img
            src={flowkar}
            className="w-16 sm:w-20 md:w-28"
            alt="Flowkar Logo"
          />
          <div className="flex gap-2 md:gap-4">
            <button
              className="px-2 md:px-4 py-1 md:py-2 text-[#563D39] text-sm md:text-sm font-medium"
              onClick={handleRedirectSignUp}
            >
              Sign Up
            </button>
            <button
              className="bg-[#563D39] rounded-lg px-2 md:px-3 py-1 flex items-center"
              onClick={handleRedirect}
            >
              <span className="text-sm md:text-sm font-medium text-white">
                Log In
              </span>
              <span className="text-white ms-1 md:ms-2 text-lg md:text-xl">
                →
              </span>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-5xl mx-auto w-full px-3 sm:px-6 lg:px-8 py-2 md:py-4">
        {loading ? (
          <div
            className="flex flex-col md:flex-row gap-4"
            style={{ height: "calc(100vh - 100px)" }}
          >
            <div className="border shadow-md rounded-lg w-full md:w-3/5 p-4 bg-white h-full">
              <LoadingSkeleton />
            </div>
            <div className="border shadow-md rounded-lg w-full md:w-2/5 p-4 bg-white h-full">
              <div className="h-16 mb-4">
                <div className="flex items-center space-x-3">
                  <div className="rounded-full bg-gray-200 h-12 w-12"></div>
                  <div className="h-4 bg-gray-200 rounded w-24"></div>
                </div>
              </div>
              <div className="h-[calc(100%-80px)] overflow-hidden">
                <CommentsSkeleton />
              </div>
            </div>
          </div>
        ) : noData ? (
          <div
            className="flex justify-center items-center"
            style={{ height: "calc(100vh - 100px)" }}
          >
            <NoData />
          </div>
        ) : error ? (
          <div
            className="flex justify-center items-center text-red-500 bg-white rounded-lg shadow-md"
            style={{ height: "calc(100vh - 100px)" }}
          >
            <div className="text-center">
              <p className="text-lg mb-4">{error}</p>
              <button
                className="px-4 py-2 bg-[#563D39] text-white rounded-lg"
                onClick={() => window.location.reload()}
              >
                Retry
              </button>
            </div>
          </div>
        ) : (
          <div
            className="flex flex-col md:flex-row gap-4"
            style={{ height: "calc(100vh - 100px)" }}
          >
            {/* Left Content - Post */}
            <div className="border shadow-md rounded-lg w-full md:w-3/5 bg-white flex flex-col h-full">
              <div className="p-3 md:p-4 flex-shrink-0">
                <div className="flex items-center space-x-3">
                  {/*  ProfileImage component */}
                  <ProfileImage
                    src={singlePost.results?.data?.[0]?.user?.profile_image}
                    alt={userName}
                    className="w-8 h-8 md:w-10 md:h-10 rounded-full"
                    size="large"
                  />
                  <div>
                    <h3 className="font-bold text-sm md:text-base">
                      {userName}
                    </h3>
                    <p className="flex items-center text-xs md:text-sm">
                      <span className="font-medium">
                        @{singlePost.results?.data?.[0]?.user?.username}
                      </span>
                      <LucideDot size={14} />
                      <button
                        className="font-bold text-[#1D9BF0]"
                        onClick={handleRedirect}
                      >
                        {isFollowing ? "Following" : "Follow"}
                      </button>
                    </p>
                  </div>
                </div>

                <p className="font-normal text-sm md:text-base text-left pt-2 md:pt-3">
                  {singlePost.results?.data?.[0]?.description}
                </p>
              </div>

              <div className="px-3 md:px-4 flex-grow flex flex-col justify-center min-h-0">
                {postFiles.length > 0 && (
                  <Suspense
                    fallback={
                      <div className="w-full aspect-square md:aspect-video flex items-center justify-center bg-gray-100 rounded-lg">
                        <Loader2 className="w-8 h-8 animate-spin text-gray-400" />
                      </div>
                    }
                  >
                    <MediaContent postFiles={postFiles} fileTypes={fileTypes} />
                  </Suspense>
                )}
              </div>

              {/* Social Media Links Section */}
              <div className="flex flex-row justify-start ms-3 md:ms-4 mb-4 md:mb-6 mt-3 items-center gap-2 flex-shrink-0">
                {socialLinks.map((linkObject, index) => {
                  const platformEntry = Object.entries(linkObject).find(
                    ([key, value]) => key !== "link" && value === true
                  );

                  if (!platformEntry) return null;
                  const [platformName] = platformEntry;
                  const iconSrc = platformToIcon[platformName];

                  if (!iconSrc) return null;
                  const link = linkObject.link;

                  return (
                    <a
                      key={index}
                      href={link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="hover:opacity-90 transition-opacity"
                    >
                      <img
                        src={iconSrc}
                        alt={`${platformName} icon`}
                        className="w-6 h-6 md:w-8 md:h-8 rounded-full"
                      />
                    </a>
                  );
                })}
              </div>
            </div>

            {/* Right Content - Comments */}
            <div className="bg-white w-full md:w-2/5 shadow-md rounded-lg flex flex-col h-full">
              <div className="flex justify-between items-center p-3 flex-shrink-0 bg-white border-b">
                <div className="flex items-center space-x-3">
                  {/*  ProfileImage component */}
                  <ProfileImage
                    src={singlePost.results?.data?.[0]?.user?.profile_image}
                    alt={userName}
                    className="w-8 h-8 md:w-10 md:h-10 rounded-full"
                  />
                  <h3 className="font-medium text-sm md:text-base">
                    {userName}
                  </h3>
                </div>
              </div>

              <div className="overflow-y-auto flex-grow min-h-0">
                <div className="flex pt-3 px-3 space-x-3">
                  {/*  ProfileImage component */}
                  <ProfileImage
                    src={singlePost.results?.data?.[0]?.user?.profile_image}
                    alt={userName}
                    className="w-8 h-8 md:w-10 md:h-10 rounded-full"
                  />
                  <div className="text-xs mb-2">
                    <p className="mb-2 text-sm font-normal">
                      <span className="font-bold text-sm">
                        {singlePost.results?.data?.[0]?.user?.username}
                      </span>{" "}
                      {singlePost.results?.data?.[0]?.description}
                    </p>
                    <p className="font-normal text-xs text-gray-400 pt-2">
                      {formatTimeDiff(createdAt)}
                    </p>
                  </div>
                </div>

                <div className="px-3 space-y-3 mt-3">
                  {commentsData.length > 0 ? (
                    commentsData.map((comment, index) => (
                      <div
                        key={comment.id || index}
                        className="flex items-start space-x-3"
                      >
                        {/* Replace img with ProfileImage component */}
                        <ProfileImage
                          src={comment.user?.profile_image}
                          alt={comment.user?.username}
                          className="w-7 h-7 md:w-8 md:h-8 rounded-full"
                          size="small"
                        />
                        <div className="flex-1 overflow-hidden">
                          <p className="text-sm break-words leading-snug">
                            <span className="font-bold">
                              {comment.user?.username}
                            </span>{" "}
                            {comment.comment_text}
                          </p>
                          <div className="flex items-center text-xs text-[#737373] pt-1 space-x-2">
                            <span>{formatTimeDiff(comment.created_at)}</span>
                            {comment.likes_count > 0 && (
                              <span>
                                · {comment.likes_count} like
                                {comment.likes_count > 1 ? "s" : ""}
                              </span>
                            )}
                          </div>
                        </div>
                        <img
                          src={Link_icon}
                          alt="like"
                          className="w-3 h-3 md:w-4 md:h-4 mt-1 cursor-pointer flex-shrink-0"
                          onClick={handleRedirect}
                        />
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-6 text-gray-500 text-sm">
                      No comments yet
                    </div>
                  )}
                </div>
              </div>

              {/* Bottom Action Section */}
              <div className="flex-shrink-0 border-t">
                <div className="px-3 pt-2">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-3 md:gap-4">
                      <button onClick={handleRedirect}>
                        <img
                          src={isLiked ? Link_icon_Active : Link_icon}
                          alt=""
                          className="w-5 h-5 md:w-6 md:h-6"
                        />
                      </button>
                      <button onClick={handleRedirect}>
                        <img
                          src={Comment_icon}
                          alt=""
                          className="w-5 h-5 md:w-6 md:h-6"
                        />
                      </button>
                      <button onClick={handleRedirect}>
                        <img
                          src={Share_icon}
                          alt=""
                          className="w-5 h-5 md:w-6 md:h-6"
                        />
                      </button>
                    </div>
                    <button onClick={handleRedirect}>
                      <img
                        src={isSaved ? Save_icon_Active : Save_icon}
                        alt=""
                        className="w-5 h-5 md:w-6 md:h-6"
                      />
                    </button>
                  </div>
                  <div className="pt-1 md:pt-2">
                    <p className="text-sm font-normal">
                      {singlePost.results?.data?.[0]?.likes} Likes
                    </p>
                  </div>
                </div>

                <div className="flex space-x-3 px-3 py-2 justify-between items-center border-t mt-2">
                  <button onClick={handleRedirect}>
                    <img
                      src={Emonji_Icon}
                      alt=""
                      className="w-5 h-5 md:w-6 md:h-6"
                    />
                  </button>
                  <div className="flex-1">
                    <input
                      type="text"
                      placeholder="Add a comment…"
                      className="text-sm font-normal outline-none w-full"
                      onClick={handleRedirect}
                    />
                  </div>
                  <button onClick={handleRedirect}>
                    <p className="text-sm font-normal text-[#A9ABAD]">Post</p>
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default SharePost;

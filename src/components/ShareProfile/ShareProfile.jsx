import React, { useEffect, useState } from "react";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import apiInstance from "../../Axios/axiosInstance";
import instagramIcon from "../../assets/svg_icon/instagram.svg";
import linkedinIcon from "../../assets/svg_icon/linkdin-icon.svg";
import pinterestIcon from "../../assets/svg_icon/pintrest-icon.svg";
import facebookIcon from "../../assets/svg_icon/facebook-icon.svg";
import tiktokIcon from "../../assets/svg_icon/tiktok-icon.svg";
import vimeoIcon from "../../assets/svg_icon/vimeo-icon.svg";
import youtubeIcon from "../../assets/svg_icon/youtube-icon.svg";
import threadIcon from "../../assets/svg_icon/thread.svg";
import dummyProfile from "../../assets/svg_icon/dummy_profile.svg";
import flowkar from "../../assets/svg_icon/Flowkar.svg";
import mastodon from "../../assets/svg_icon/mastodon-icon.svg";

const SOCIAL_ICONS = {
  INSTAGRAM_ICON: instagramIcon,
  LINKEDIN_ICON: linkedinIcon,
  PINTEREST_ICON: pinterestIcon,
  FACEBOOK_ICON: facebookIcon,
  TIKTOK_ICON: tiktokIcon,
  VIMEO_ICON: vimeoIcon,
  YOUTUBE_ICON: youtubeIcon,
  THREAD_ICON: threadIcon,
  DUMMY_PROFILE: dummyProfile,
  FLOWKAR_LOGO: flowkar,
  MASTODON_ICON: mastodon,
};

// Platform to gradient colors mapping
const platformGradients = {
  instagram: ["#f58529", "#dd2a7b"],
  facebook: ["#1877F2", "#3b5998"],
  youtube: ["#ff4e50", "#f9d423"],
  pinterest: ["#cb2027", "#ff7171"],
  linkedin: ["#0072b1", "#00c6ff"],
  vimeo: ["#00b3ec", "#3f9db3"],
  tiktok: ["#ff0050", "#00f2ea"],
  thread: ["#2c2c2c", "#000000"],
  reddit: ["#ff5700", "#fbc02d"],
  tumblr: ["#34526f", "#6a7ba2"],
  mastodon: ["#6364FF", "#AB52FF"],
};

const ShareProfile = () => {
  const { username } = useParams();
  const [sharedata, setSharedata] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const brandId = searchParams.get("brand_id");

  useEffect(() => {
    if (brandId) {
      const fetchSharedata = async () => {
        try {
          const { data } = await apiInstance.get(`/share-profile/`, {
            headers: {
              brand: brandId,
            },
          });
          console.log("API Response Data:", data);
          setSharedata([data?.profile]);
        } catch (error) {
          setError("Error fetching, Please try again.");
        } finally {
          setLoading(false);
        }
      };

      fetchSharedata();
    } else {
      setLoading(false);
    }
  }, [username, brandId]);

  // Helper function to get the correct icon for the platform
  const getPlatformIcon = (platform) => {
    const platformUpper = platform.toUpperCase();
    return SOCIAL_ICONS[`${platformUpper}_ICON`] || SOCIAL_ICONS.DUMMY_PROFILE;
  };

  // Function to get gradient colors for a platform
  const getGradientColors = (platform) => {
    return platformGradients[platform.toLowerCase()] || ["#805835", "#98775E"];
  };

  // Function to render the 'No profiles found' message
  const renderNoProfilesFound = () => {
    return (
      <div className="flex flex-col items-center justify-center mt-20 p-6">
        <img
          src={SOCIAL_ICONS.FLOWKAR_LOGO}
          alt="Flowkar Logo"
          className="h-20 w-20 mb-6 opacity-50"
        />
        <p className="text-center text-gray-600 text-xl font-medium">
          No profiles found
        </p>
        <p className="text-center text-gray-500 mt-2">
          We couldn't find any profiles to display
        </p>
        <button
          onClick={() => navigate("/")}
          className="mt-6 bg-[#563D39] text-white font-semibold py-3 px-6 rounded-lg text-sm sm:text-base hover:bg-opacity-90 transition-all"
        >
          Return Home
        </button>
      </div>
    );
  };

  // Check if we have valid profiles with social links to display
  const hasProfilesToShow =
    sharedata &&
    sharedata.length > 0 &&
    sharedata[0]?.social_links &&
    sharedata[0].social_links.filter((link) => link.url).length > 0;

  return (
    <div className="font-Ubuntu min-h-screen overflow-y-auto bg-[#FFFFFF] w-full">
      <div className="bg-white shadow-lg">
        <div className="max-w-[90%] mx-auto w-full">
          <img
            src={SOCIAL_ICONS.FLOWKAR_LOGO}
            className="h-14 w-20 sm:h-16 sm:w-16 md:h-20 md:w-32"
            alt="Flowkar Logo"
          />
        </div>
      </div>

      {/* Display loader while data is being fetched */}
      {loading ? (
        <div className="flex items-center justify-center mt-20">
          <p className="text-center text-Red font-medium text-lg">
            Loading, Please wait...
          </p>
        </div>
      ) : error ? (
        <div className="flex items-center justify-center mt-20">
          <p className="text-center text-Red font-medium text-lg">{error}</p>
        </div>
      ) : hasProfilesToShow ? (
        <div className="max-w-[90%] mx-auto w-full">
          {/* User Profile Section - Added from second code block */}
          <div className="max-w-[90%] mx-auto w-full">
            <div
              className=" relative h-24 rounded-b-full"
              style={{
                background: "linear-gradient(90deg, #805835 0%, #98775E 100%)",
              }}
            >
              <div className="absolute top-12 left-1/2 transform -translate-x-1/2">
                <div className="w-24 h-24 rounded-full overflow-hidden border-4 border-white">
                  <div className="w-full h-full bg-gray-300 flex items-center justify-center">
                    <img
                      src={sharedata[0]?.profile_picture}
                      alt="User Profile"
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-16">
              <p className="text-center text-[#563D39] font-medium text-base">
                {sharedata[0]?.name || "Alexander Daddario"}
              </p>
              <p className="text-center text-[#563D39] font-normal text-base">
                @{sharedata[0]?.username || "Beginner"}
              </p>
            </div>
          </div>

          {/* Brand title */}
          <div className="mt-5 pb-10">
            <p className="text-[#563D39] font-medium text-2xl">
              {sharedata[0]?.brand_detail?.name}
            </p>

            {/* Grid Layout */}
            <div className="mt-5 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
              {sharedata.map((profile) =>
                profile.social_links && profile.social_links.length > 0
                  ? profile.social_links
                      .filter((link) => link.url)
                      .map((link) => {
                        const gradientColors = getGradientColors(link.platform);

                        return (
                          <div
                            key={link.platform}
                            className="bg-white border ring-slate-900 rounded-[18px] p-1 flex flex-col h-full shadow-md"
                          >
                            {/* Gradient Header */}
                            <div
                              className="rounded-[18px] relative h-24"
                              style={{
                                background: `linear-gradient(90deg, ${gradientColors[0]} 0%, ${gradientColors[1]} 100%)`,
                              }}
                            ></div>

                            {/* Profile Image */}
                            <div className="absolute transform -translate-x-1/2 mt-14 ml-16">
                              <div className="w-20 h-20 rounded-full overflow-hidden border-4 border-white">
                                <div className="w-full h-full bg-gray-300 flex items-center justify-center">
                                  <img
                                    src={
                                      link.profile_image ||
                                      SOCIAL_ICONS.DUMMY_PROFILE
                                    }
                                    alt="Profile"
                                    className="w-full h-full object-cover"
                                  />
                                </div>
                              </div>
                            </div>

                            {/* Profile Info */}
                            <div className="pt-12 ps-6">
                              <p className="text-base font-bold">
                                {link.name || "User"}
                              </p>
                              <p className="text-xs font-medium">
                                {link.username || ""}
                              </p>
                              <div className="flex items-center mt-4">
                                <img
                                  src={getPlatformIcon(link.platform)}
                                  className="w-9 h-9 rounded-full"
                                  alt={`${link.platform} icon`}
                                />
                                <p className="text-base font-normal ps-2">
                                  {link.platform}
                                </p>
                              </div>
                            </div>

                            {/* Visit Profile Button */}
                            <div className="px-4 pb-4 bg-white pt-9 mt-auto rounded-[18px]">
                              <a
                                href={link.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="block w-full border border-gray-300 rounded-lg py-2 text-base font-normal text-center transition-colors hover:bg-gray-50"
                              >
                                Visit Profile
                              </a>
                            </div>
                          </div>
                        );
                      })
                  : null
              )}
            </div>
          </div>
        </div>
      ) : (
        // No profiles found - show the "No profiles found" message
        renderNoProfilesFound()
      )}
    </div>
  );
};

export default ShareProfile;

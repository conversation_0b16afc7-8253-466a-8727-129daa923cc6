import React, { useState } from "react";

const ProfileImage = ({ src, alt, className, size = "normal" }) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  
  // Determine size for skeleton placeholder
  const getSizeClasses = () => {
    if (className) return className; // Use provided classes if available
    
    // Default sizes
    switch (size) {
      case "small":
        return "w-8 h-8";
      case "large":
        return "w-12 h-12";
      case "normal":
      default:
        return "w-10 h-10";
    }
  };
  
  const sizeClasses = getSizeClasses();
  
  // Generate initials from alt text if available
  const getInitials = () => {
    if (!alt || typeof alt !== 'string') return '?';
    return alt.split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  return (
    <div className={`${sizeClasses} relative rounded-full overflow-hidden flex-shrink-0`}>
      {/* Skeleton placeholder */}
      {!imageLoaded && !imageError && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse rounded-full" />
      )}
      
      {/* Fallback for error */}
      {imageError && (
        <div className="absolute inset-0 bg-gray-300 rounded-full flex items-center justify-center">
          <span className="text-gray-600 text-xs font-medium">
            {getInitials()}
          </span>
        </div>
      )}
      
      {/* Actual image */}
      <img
        src={src}
        alt={alt || "User profile"}
        className={`rounded-full object-cover w-full h-full ${!imageLoaded ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
        onLoad={() => setImageLoaded(true)}
        onError={() => setImageError(true)}
        loading="lazy"
      />
    </div>
  );
};

export default ProfileImage;
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { createSlug } from "../../utils/slugUtils";
import Bg from "../../assets/Blog/Blog_bg.svg";
import Image_01 from "../../assets/Blog/Image_01.svg";
import Image_02 from "../../assets/Blog/Image_02.svg";
import Image_03 from "../../assets/Blog/Image_03.svg";
import Image_04 from "../../assets/Blog/Image_04.svg";
import Image_05 from "../../assets/Blog/Image_05.svg";
import Image_06 from "../../assets/Blog/Image_06.svg";
import Image_07 from "../../assets/Blog/Image_07.svg";
import Image_08 from "../../assets/Blog/Image_08.svg";
import Image_09 from "../../assets/Blog/Image_09.svg";
import Image_10 from "../../assets/Blog/Image_10.svg";
import Image_11 from "../../assets/Blog/Image_11.svg";
import Image_12 from "../../assets/Blog/Image_12.svg";
import Image_13 from "../../assets/Blog/Image_13.svg";
import Image_14 from "../../assets/Blog/Image_14.svg";
import Image_15 from "../../assets/Blog/Image_15.svg";
import Image_16 from "../../assets/Blog/Image_16.svg";
import Image_17 from "../../assets/Blog/Image_17.svg";
import Image_18 from "../../assets/Blog/Image_18.svg";
import Image_19 from "../../assets/Blog/Image_19.svg";
import Search from "../../assets/Blog/Search.svg";

// Static data moved outside component to prevent re-creation on every render
const blogImages = [
  Image_01,
  Image_02,
  Image_03,
  Image_04,
  Image_05,
  Image_06,
  Image_07,
  Image_08,
  Image_09,
  Image_10,
  Image_11,
  Image_12,
  Image_13,
  Image_14,
  Image_15,
  Image_16,
  Image_17,
  Image_18,
  Image_19,
];

// Skeleton Components
const SkeletonBlogCard = () => (
  <div className="bg-white rounded-[16px] min-h-[280px] sm:min-h-[311px] w-full flex flex-col border-[1px] border-[#0000001A] shadow-sm">
    <div className="rounded-tl-[16px] rounded-tr-[16px] mb-3 h-[160px] sm:h-[200px] w-full bg-gray-200 animate-pulse"></div>
    <div className="flex flex-col flex-1 px-3 pb-3">
      <div className="h-4 bg-gray-200 rounded mb-2 animate-pulse"></div>
      <div className="h-4 bg-gray-200 rounded mb-2 w-3/4 animate-pulse"></div>
      <div className="flex items-center justify-between mt-auto">
        <div className="h-3 bg-gray-200 rounded w-16 animate-pulse"></div>
        <div className="h-3 bg-gray-200 rounded w-20 animate-pulse"></div>
      </div>
    </div>
  </div>
);

const SkeletonFeaturedBlog = () => (
  <div className="flex flex-col xl:flex-row gap-6 lg:gap-8 items-start mb-8 sm:mb-12 lg:mb-[20px]">
    {/* Featured Image Skeleton */}
    <div className="w-full xl:flex-1">
      <div className="bg-gray-200 rounded-[12px] sm:rounded-[16px] lg:rounded-[20px] w-full h-[200px] sm:h-[250px] lg:h-[297px] animate-pulse"></div>
    </div>

    {/* Featured Content Skeleton */}
    <div className="w-full xl:w-96 xl:flex-shrink-0">
      <div className="flex flex-col">
        <div className="h-6 bg-gray-200 rounded mb-3 animate-pulse"></div>
        <div className="h-6 bg-gray-200 rounded mb-3 w-5/6 animate-pulse"></div>
        <div className="h-6 bg-gray-200 rounded mb-3 w-4/6 animate-pulse"></div>
        <div className="h-4 bg-gray-200 rounded mb-4 sm:mb-6 w-full animate-pulse"></div>
        <div className="h-4 bg-gray-200 rounded mb-4 sm:mb-6 w-3/4 animate-pulse"></div>
        <div className="flex items-center justify-between">
          <div className="h-3 bg-gray-200 rounded w-20 animate-pulse"></div>
          <div className="h-3 bg-gray-200 rounded w-16 animate-pulse"></div>
        </div>
      </div>
    </div>
  </div>
);

const SkeletonRecentPost = () => (
  <div className="flex gap-3 items-start">
    <div className="rounded-lg w-[130px] h-[120px] bg-gray-200 animate-pulse flex-shrink-0"></div>
    <div className="flex-1 min-w-0">
      <div className="h-3 bg-gray-200 rounded w-16 mb-2 animate-pulse"></div>
      <div className="h-4 bg-gray-200 rounded mb-1 animate-pulse"></div>
      <div className="h-4 bg-gray-200 rounded mb-1 w-3/4 animate-pulse"></div>
      <div className="h-3 bg-gray-200 rounded w-12 animate-pulse"></div>
    </div>
  </div>
);

const SkeletonSidebar = () => (
  <div className="w-full xl:w-96 xl:flex-shrink-0 xl:self-stretch mt-6 xl:mt-0">
    <div className="flex flex-col gap-4 sm:gap-6 h-full">
      {/* Search Skeleton */}
      <div className="bg-white rounded-[100px] shadow-sm border border-gray-100 p-4 flex items-center gap-3">
        <div className="w-5 h-5 bg-gray-200 rounded animate-pulse"></div>
        <div className="flex-1 h-4 bg-gray-200 rounded animate-pulse"></div>
      </div>

      {/* Recent Posts Skeleton */}
      <div className="bg-white rounded-xl p-4 flex flex-col flex-1 md:pb-[120px]">
        <div className="h-5 bg-gray-200 rounded w-24 mb-4 animate-pulse"></div>
        <div className="flex flex-col gap-4">
          {[1, 2, 3, 4].map((index) => (
            <SkeletonRecentPost key={index} />
          ))}
        </div>
      </div>
    </div>
  </div>
);

// Extracted components for better performance and readability
const BlogCard = React.memo(({ image, title, date, category, onClick }) => (
  <div
    className="bg-white rounded-[16px] min-h-[280px] sm:min-h-[311px] w-full flex flex-col shadow-sm hover:shadow-lg  transition-all  cursor-pointer duration-[400ms]"
    onClick={onClick}
  >
    <img
      src={image}
      alt="Blog"
      className="rounded-tl-[16px] rounded-tr-[16px] mb-3 object-cover h-[160px] sm:h-[200px] w-full"
      loading="lazy"
    />
    <div className="flex flex-col flex-1 px-3 pb-3">
      <h3 className="font-semibold text-base sm:text-lg mb-2 flex-1 line-clamp-2">
        {title}
      </h3>
      <div className="flex items-center justify-between text-xs text-[#00000099] mt-auto">
        <span>{date}</span>
        <span>{category}</span>
      </div>
    </div>
  </div>
));

const RecentPost = React.memo(
  ({ image, title, category, date, name, read, onClick }) => (
    <div
      className="flex gap-3 items-start rounded-[16px] shadow-sm hover:shadow-lg transition-all  cursor-pointer duration-[400ms]"
      onClick={onClick}
    >
      <img
        src={image}
        alt="Recent"
        className="rounded-lg w-[130px] h-[120px] object-cover flex-shrink-0"
        loading="lazy"
      />
      <div className="flex-1 min-w-0">
        <div className="text-xs text-gray-400 flex items-center gap-2 flex-wrap">
          <span className="">{date}</span>
        </div>
        <h5 className="text-sm font-semibold leading-tight line-clamp-2 mb-1">
          {title}
        </h5>
        <div className="text-xs text-gray-400 flex items-center gap-2 flex-wrap">
          <span className="">{read}</span>
        </div>
      </div>
    </div>
  )
);

// Main component
function BlogsHero() {
  const navigate = useNavigate();
  const [blogs, setBlogs] = useState([]);
  const [featuredBlog, setFeaturedBlog] = useState(null);
  const [mainBlogs, setMainBlogs] = useState([]);
  const [sidebarBlogs, setSidebarBlogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState([]);

  useEffect(() => {
    // For modern browsers
    const navEntry = window.performance.getEntriesByType("navigation")[0];
    if (
      (navEntry && navEntry.type === "reload") ||
      window.performance.navigation.type === 1
    ) {
      localStorage.removeItem("flowkar_blogs");
    }
  }, []);

  const BlogData = async () => {
    setLoading(true);
    setError(null);
    try {
      // Check localStorage first
      const cachedBlogs = localStorage.getItem("flowkar_blogs");
      if (cachedBlogs) {
        const parsedBlogs = JSON.parse(cachedBlogs);
        setBlogs(parsedBlogs);
        setFeaturedBlog(parsedBlogs[0] || null);
        setMainBlogs(parsedBlogs.slice(1, 5));
        setSidebarBlogs(parsedBlogs.slice(5, 9));
        setLoading(false);
        return;
      }
      const response = await fetch("https://flowkar.com/api/get-all-blogs/");
      if (!response.ok) throw new Error("Network response was not ok");
      const data = await response.json();
      // Map API data to BlogCard format
      const mappedBlogs = (data?.data || []).map((item) => ({
        id: item.id,
        image: item.video_thumbnail
          ? item.video_thumbnail.startsWith("http")
            ? item.video_thumbnail
            : `https://flowkar.com${
                item.video_thumbnail.startsWith("/")
                  ? item.video_thumbnail
                  : "/" + item.video_thumbnail
              }`
          : item.banner
          ? item.banner.startsWith("http")
            ? item.banner
            : `https://flowkar.com${
                item.banner.startsWith("/") ? item.banner : "/" + item.banner
              }`
          : Image_01, // fallback to Image_01 if no video_thumbnail or banner
        title: item.title || "Untitled Blog",
        date: item.created_at
          ? new Date(item.created_at).toLocaleDateString()
          : "",
        category: item.category || "General",
        author: item.keywords || "Unknown Author",
        readTime: item.read_time || "",
        excerpt: item.keywords || "",
        content: item.keywords || "",
        tags: item.keywords ? item.keywords.split(",") : [],
        created_at: item.created_at || "", // Keep original date for sorting
      }));
      // Sort blogs by created_at descending (latest first)
      mappedBlogs.sort(
        (a, b) => new Date(b.created_at) - new Date(a.created_at)
      );
      // Store in localStorage
      localStorage.setItem("flowkar_blogs", JSON.stringify(mappedBlogs));
      setBlogs(mappedBlogs);
      setFeaturedBlog(mappedBlogs[0] || null);
      setMainBlogs(mappedBlogs.slice(1, 5));
      setSidebarBlogs(mappedBlogs.slice(5, 9));
    } catch (err) {
      setError("Failed to fetch blogs.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    BlogData();
  }, []);

  // Search effect
  useEffect(() => {
    if (searchQuery.trim() === "") {
      setSearchResults([]);
      return;
    }
    const results = blogs.filter(
      (blog) =>
        blog.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        blog.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (blog.author &&
          blog.author.toLowerCase().includes(searchQuery.toLowerCase()))
    );
    setSearchResults(results.slice(0, 4));
  }, [searchQuery, blogs]);

  const handleBlogClick = (blog) => {
    const blogSlug = createSlug(blog.title);
    navigate(`/blog/${blogSlug}`);
  };

  const handleRecentPostClick = (post) => {
    const postSlug = createSlug(post.title);
    navigate(`/blog/${postSlug}`);
  };

  return (
    <div className="min-h-screen px-4 sm:px-6 lg:px-6 mt-[120px] sm:mt-[80px] w-[90%] mx-auto font-figtree">
      {/* Header Section */}
      <header
        className="bg-[#5a3a32] rounded-[12px] sm:rounded-[16px] lg:rounded-[20px] text-white py-8 sm:py-12 lg:py-[70px] px-4 sm:px-6 flex flex-col items-center relative overflow-hidden mt-8 sm:mt-12 lg:mt-[160px] mb-8 sm:mb-16 lg:mb-[120px]"
        style={{
          backgroundImage: `url(${Bg})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      >
        <h1 className="text-2xl sm:text-3xl lg:text-4xl font-semibold mb-2 text-center leading-tight">
          Stories That Shape Tomorrow.
        </h1>
        <p className="text-base sm:text-lg lg:text-xl font-normal text-center opacity-80 max-w-2xl">
          A sharp, on-brand blog title that's ready to roll.
        </p>
      </header>

      {/* Featured Blog Section */}
      {loading ? (
        <SkeletonFeaturedBlog />
      ) : featuredBlog ? (
        <div className="flex flex-col xl:flex-row gap-6 lg:gap-8 items-start mb-8 sm:mb-12 lg:mb-[20px] ">
          {/* Featured Image */}
          <div className="w-full xl:flex-1">
            <article
              className="flex flex-col bg-white rounded-xl  cursor-pointer "
              onClick={() => handleBlogClick(featuredBlog)}
            >
              <img
                src={featuredBlog.image}
                alt="Featured blog post"
                className="rounded-[12px] sm:rounded-[16px] lg:rounded-[20px] w-full object-cover h-[200px] sm:h-[250px] lg:h-[297px]"
                loading="eager"
              />
            </article>
          </div>

          {/* Featured Content */}
          <div className="w-full xl:w-96 xl:flex-shrink-0">
            <div className="flex flex-col">
              <h2
                className="text-xl sm:text-2xl lg:text-[34px] font-normal mb-3 sm:mb-4 leading-tight cursor-pointer hover:text-gray-700 transition-colors"
                onClick={() => handleBlogClick(featuredBlog)}
              >
                {featuredBlog.title}
              </h2>
              <p className="text-[#00000080] font-light text-base sm:text-lg lg:text-[22px] mb-4 sm:mb-6 leading-relaxed">
                {featuredBlog.excerpt}
              </p>
              <div className="flex items-center justify-between text-sm text-gray-400">
                <time>{featuredBlog.date}</time>
                <span>{featuredBlog.category}</span>
              </div>
            </div>
          </div>
        </div>
      ) : null}

      {/* Main Content Grid */}
      <div className="flex flex-col xl:flex-row gap-6 lg:gap-8 items-start w-full xl:items-stretch">
        {/* Blog Grid */}
        <main className="w-full xl:flex-1">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 xl:pb-[120px] h-full">
            {loading ? (
              // Show skeleton loaders for main blog grid
              Array.from({ length: 4 }).map((_, index) => (
                <SkeletonBlogCard key={index} />
              ))
            ) : error ? (
              <div className="col-span-full text-center text-red-500 py-8">
                {error}
              </div>
            ) : mainBlogs.length > 0 ? (
              mainBlogs.map((card) => (
                <BlogCard
                  key={card.id}
                  image={card.image}
                  title={card.title}
                  date={card.date}
                  category={card.category}
                  onClick={() => handleBlogClick(card)}
                />
              ))
            ) : (
              <div className="col-span-full text-center py-8">
                No blogs found.
              </div>
            )}
          </div>
        </main>

        {/* Sidebar - Matches blog grid height exactly */}
        {loading ? (
          <SkeletonSidebar />
        ) : (
          <aside className="w-full xl:w-96 xl:flex-shrink-0 xl:self-stretch mt-6 xl:mt-0">
            <div className="flex flex-col gap-4 sm:gap-6 h-full">
              {/* Search */}
              <div className="bg-white rounded-[100px] shadow-sm border-[1px] border-[#563d39bf] p-4 flex items-center gap-3">
                <img src={Search} alt="Search" className="" />
                <input
                  type="text"
                  placeholder="Search..."
                  className="flex-1 outline-none bg-transparent text-sm sm:text-base"
                  aria-label="Search blog posts"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              {/* Recent Posts or Search Results */}
              <div className="bg-white rounded-xl p-4 flex flex-col flex-1 md:pb-[120px]">
                {searchQuery && searchResults.length > 0 ? (
                  <>
                    <h4 className="font-semibold text-gray-700 mb-4">
                      Search Results
                    </h4>
                    <div className="flex flex-col gap-4">
                      {searchResults.map((post) => (
                        <RecentPost
                          key={post.id}
                          image={post.image}
                          title={post.title}
                          category={post.category}
                          date={post.date}
                          name={post.author}
                          read={post.readTime}
                          onClick={() => handleBlogClick(post)}
                        />
                      ))}
                    </div>
                  </>
                ) : (
                  <>
                    <h4 className="font-semibold text-gray-700 mb-4">
                      Shortcut tags
                    </h4>
                    <div className="flex flex-col gap-4">
                      {sidebarBlogs.map((post) => (
                        <RecentPost
                          key={post.id}
                          image={post.image}
                          title={post.title}
                          category={post.category}
                          date={post.date}
                          name={post.author}
                          read={post.readTime}
                          onClick={() => handleBlogClick(post)}
                        />
                      ))}
                    </div>
                  </>
                )}
              </div>
            </div>
          </aside>
        )}
      </div>
    </div>
  );
}

export default BlogsHero;

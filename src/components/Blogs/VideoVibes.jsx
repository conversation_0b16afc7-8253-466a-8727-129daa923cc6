import React from "react";
import { useNavigate } from "react-router-dom";
import { createSlug } from "../../utils/slugUtils";
import Image_18 from "../../assets/Blog/Image_18.svg";
import Image_19 from "../../assets/Blog/Image_19.svg";

const PlayIcon = () => (
  <div className="absolute inset-0 flex items-center justify-center z-10">
    <div className="h-[92px] w-[92px] border-[1px] border-[#FFFFFF66] rounded-full flex items-center justify-center">
      <div className="h-[76px] w-[76px] rounded-full p-[8px] bg-[#FFFFFF3D] flex items-center justify-center">
        <svg
          className="w-[40px] h-[40px] text-white"
          fill="currentColor"
          viewBox="0 0 24 24"
        >
          <path d="M8 5v14l11-7z" />
        </svg>
      </div>
    </div>
  </div>
);

const VideoCard = ({
  image,
  title,
  excerpt,
  category,
  readTime,
  hasPlayButton = false,
  onClick,
}) => (
  <div className="relative h-full flex flex-col cursor-pointer transition-transform duration-300" onClick={onClick}>
    <div className="relative overflow-hidden rounded-[24px] mb-4">
      <img
        src={image}
        alt={title}
        className="w-full h-[270px] object-cover border-[1px] border-[#00000080]"
      />
      {hasPlayButton && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className=" flex items-center justify-center">
            <PlayIcon />
          </div>
        </div>
      )}
    </div>
    <div className="flex-1 flex flex-col">
      <h3 className="text-base font-semibold text-gray-900 leading-snug mb-2">
        {title}
      </h3>
      <p className="text-sm text-gray-600 leading-5 mb-2 flex-1">{excerpt}</p>
      <p className="text-xs text-gray-400">
        <span className="mr-2">{category}</span> | <span className="ml-2">{readTime}</span>
      </p>
    </div>
  </div>
);

const mainCardData = {
  id: 201,
  title: "A Day in the Life of an Astronaut: Humanity's Open Window to Space",
  excerpt:
    "Astronauts frequently use the definition of freedom related to space, yet their experiences not only give them a sense of being a speck in the universe but also offer the opportunity to witness breathtaking views. Astronauts frequently use the...",
  category: "Space and Universe",
  readTime: "4 mins read",
  image: Image_18,
  author: "Dr. Sarah Chen",
  content: `A Day in the Life of an Astronaut: Humanity's Open Window to Space

    Life aboard the International Space Station (ISS) is a carefully orchestrated ballet of science, maintenance, and human endurance. Astronauts experience 16 sunrises and sunsets every day as they orbit Earth at 17,500 miles per hour, 250 miles above the planet's surface.

    A typical day begins at 6:00 AM GMT, when astronauts wake up to the sound of mission control's morning call. The first task is always checking the station's systems and communicating with ground teams about the day's schedule. Every minute of an astronaut's time in space is precious and carefully planned.

    Morning routines include personal hygiene in microgravity, which requires special techniques. Water doesn't flow in space, so astronauts use specially designed equipment for washing. Even simple tasks like brushing teeth require careful planning to prevent water droplets from floating around the station.

    The majority of an astronaut's day is spent conducting scientific experiments. These range from studying the effects of microgravity on human physiology to testing new materials and technologies. The ISS serves as a unique laboratory where researchers can study phenomena that cannot be replicated on Earth.

    Exercise is crucial for maintaining health in space. Astronauts spend at least two hours each day working out on specialized equipment to prevent muscle atrophy and bone loss. The station is equipped with treadmills, stationary bicycles, and resistance training devices.

    Evenings are spent communicating with family, documenting their experiences, and preparing for the next day's activities. The view from the station's windows provides a constant reminder of the beauty and fragility of our planet, inspiring astronauts to share their perspective with people on Earth.

    The psychological challenges of living in space are as significant as the physical ones. Astronauts must learn to work effectively in confined spaces with crew members from different cultures and backgrounds. The experience often leads to profound personal growth and a new appreciation for Earth's environment.

    The unique perspective from space has inspired countless people to pursue careers in science, technology, engineering, and mathematics. Astronauts often describe their time in space as transformative, providing them with a new understanding of our place in the universe and the importance of protecting our planet.`,
  tags: ["Astronauts", "ISS", "Space Life", "Microgravity", "Science", "Human Spaceflight"]
};

const videoCards = [
  {
    id: 202,
    title: "Ocean Depths: Exploring the Last Frontier on Earth",
    excerpt:
      "Deep sea exploration reveals mysteries that rival space discoveries, with creatures and landscapes beyond imagination.",
    category: "Marine Biology",
    readTime: "6 mins read",
    image: Image_19,
    author: "Dr. Marina Rodriguez",
    content: `Ocean Depths: Exploring the Last Frontier on Earth

    The deep ocean remains one of the least explored regions on our planet, with more than 80% of the ocean floor still unmapped. This vast underwater world holds secrets that rival the mysteries of space, with creatures and landscapes that seem to belong to another world entirely.

    Deep sea exploration has revealed extraordinary life forms that have adapted to survive in extreme conditions. From bioluminescent creatures that create their own light to giant squid that can grow up to 43 feet long, the deep ocean is home to some of the most fascinating organisms on Earth.

    The pressure at the bottom of the ocean can reach over 1,000 atmospheres, equivalent to having 50 jumbo jets stacked on top of you. Despite these extreme conditions, life thrives in the deep sea, demonstrating the incredible adaptability of living organisms.

    Recent discoveries include hydrothermal vents, where superheated water rich in minerals spews from the ocean floor. These vents support entire ecosystems of creatures that rely on chemosynthesis rather than photosynthesis, including giant tube worms and blind shrimp.

    The deep ocean also plays a crucial role in regulating Earth's climate. Ocean currents transport heat around the globe, and the deep sea stores vast amounts of carbon dioxide. Understanding these processes is essential for predicting and mitigating climate change.

    Exploration of the deep ocean requires specialized equipment, including remotely operated vehicles (ROVs) and autonomous underwater vehicles (AUVs). These sophisticated machines allow scientists to study the deep sea without the risks associated with human diving at extreme depths.

    The deep ocean also holds valuable resources, including rare earth elements and potential sources of renewable energy. However, mining these resources must be done carefully to avoid damaging fragile deep-sea ecosystems.

    As we continue to explore the deep ocean, we discover not only new species but also new insights into the origins of life on Earth. The extreme conditions of the deep sea may mirror the conditions that existed on early Earth, providing clues about how life first evolved.`,
    tags: ["Ocean Exploration", "Marine Biology", "Deep Sea", "Hydrothermal Vents", "Biodiversity"]
  },
  {
    id: 203,
    title: "Quantum Computing: The Future of Information Processing",
    excerpt:
      "Revolutionary quantum computers promise to solve problems that would take classical computers millions of years.",
    category: "Technology",
    readTime: "5 mins read",
    image: Image_19,
    author: "Dr. Alex Thompson",
    content: `Quantum Computing: The Future of Information Processing

    Quantum computing represents a revolutionary approach to information processing that harnesses the strange properties of quantum mechanics to solve problems that are currently impossible for classical computers. While traditional computers process information as bits (0s and 1s), quantum computers use quantum bits, or qubits, which can exist in multiple states simultaneously.

    The power of quantum computing lies in its ability to perform certain calculations exponentially faster than classical computers. This has profound implications for fields such as cryptography, drug discovery, materials science, and artificial intelligence.

    One of the most promising applications of quantum computing is in cryptography. Current encryption methods rely on the difficulty of factoring large numbers, a task that would take classical computers thousands of years. Quantum computers, however, could potentially break these codes in minutes using Shor's algorithm.

    In drug discovery, quantum computers could simulate molecular interactions with unprecedented accuracy, dramatically accelerating the development of new medicines. This could lead to breakthroughs in treating diseases such as cancer, Alzheimer's, and COVID-19.

    Materials science is another field that could benefit enormously from quantum computing. Simulating the behavior of materials at the quantum level could lead to the development of new superconductors, more efficient solar cells, and stronger, lighter materials for aerospace applications.

    Despite its enormous potential, quantum computing faces significant technical challenges. Qubits are extremely fragile and can lose their quantum state through a process called decoherence. Researchers are developing various approaches to address this issue, including error correction codes and different qubit technologies.

    Major technology companies, including IBM, Google, and Microsoft, are investing heavily in quantum computing research. IBM has already built quantum computers with over 100 qubits, while Google achieved "quantum supremacy" by performing a calculation that would be impossible for classical computers.

    The development of quantum computing also raises important questions about cybersecurity and the need for quantum-resistant encryption methods. Governments and organizations worldwide are working to develop new cryptographic standards that will remain secure even against quantum attacks.

    As quantum computing technology continues to advance, we can expect to see increasingly powerful quantum computers that will transform industries and solve problems that were previously thought to be unsolvable. The quantum revolution is just beginning, and its full impact on society is yet to be realized.`,
    tags: ["Quantum Computing", "Technology", "Cryptography", "Drug Discovery", "Materials Science"]
  },
];

function VideoVibes() {
  const navigate = useNavigate();

  const handleBlogClick = (blog) => {
    const blogSlug = createSlug(blog.title);
    navigate(`/blog/${blogSlug}`);
  };

  return (
    <section className="px-4 md:px-20 mt-15 py-[60px]">
      {/* Header */}
      <div className="flex justify-center items-center mb-8">
        <h2 className="text-3xl font-semibold text-[#563D39]">
          Video Vibes: Stories You Can Watch
        </h2>
        {/* <a
          href="#"
          className="text-sm font-medium text-gray-600 hover:underline"
        >
          See All &gt;
        </a> */}
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-stretch">
        {/* Left Large Card */}
        <div className="flex flex-col h-full cursor-pointer transition-transform duration-300" onClick={() => handleBlogClick(mainCardData)}>
          <div className="overflow-hidden rounded-2xl mb-4 flex-1">
            <img
              src={mainCardData.image}
              alt={mainCardData.title}
              className="w-full h-full min-h-[400px] object-cover border border-black/50 rounded-[50px]"
            />
          </div>
          <div className="flex-shrink-0">
            <h3 className="text-lg font-semibold text-gray-900 leading-6 mb-3">
              {mainCardData.title}
            </h3>
            <p className="text-sm text-gray-600 leading-5 mb-2">
              {mainCardData.excerpt}
            </p>
            <p className="text-xs text-gray-400">
              <span className="mr-2">{mainCardData.category}</span> | <span className="ml-2">{mainCardData.readTime}</span>
            </p>
          </div>
        </div>

        {/* Right Small Cards */}
        <div className="flex flex-col gap-4 h-full">
          {videoCards.map((card) => (
            <div key={card.id} className="flex-1">
              <VideoCard
                image={card.image}
                title={card.title}
                excerpt={card.excerpt}
                category={card.category}
                readTime={card.readTime}
                hasPlayButton={true}
                onClick={() => handleBlogClick(card)}
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

export default VideoVibes;
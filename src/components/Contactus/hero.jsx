import React, { useState, useEffect } from "react";
import Bg from "../../assets/Blog/Blog_bg.svg";
import Circle from "../../assets/ContactUs/Circle.svg";
import Dummy_01 from "../../assets/ContactUs/Dummy_01.svg";

// Custom skeleton for Hero section
const HeroSkeleton = () => (
  <div className="mt-[120px] w-[90%] mx-auto font-figtree animate-pulse">
    <header
      className="bg-[#5a3a32] xl:h-[522px] rounded-[12px] sm:rounded-[16px] lg:rounded-[20px] text-white px-4 sm:px-16 flex flex-col md:flex-row items-center relative justify-between mt-8 mb-8 overflow-hidden"
      style={{
        backgroundImage: `url(${Bg})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
      }}
    >
      {/* Left Side Skeleton */}
      <div className="md:w-[50%] flex flex-col md:justify-start items-center md:items-start py-8">
        <div className="h-10 bg-white bg-opacity-20 rounded w-2/3 mb-4" />
        <div className="h-8 bg-white bg-opacity-20 rounded w-1/2 mb-4" />
        <div className="h-10 bg-white bg-opacity-20 rounded w-40" />
        <div className="h-6 bg-white bg-opacity-10 rounded w-3/4 mt-8" />
      </div>
      {/* Right Side Skeleton */}
      <div className="flex justify-center items-center md:items-end md:justify-end w-full md:w-auto">
        <div className="w-[180px] h-[220px] md:w-[260px] md:h-[320px] bg-white bg-opacity-10 rounded-[40px]" />
      </div>
    </header>
  </div>
);

function Hero() {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => setLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  if (loading) {
    return <HeroSkeleton />;
  }

  return (
    <div className=" mt-[120px] sm:mt-[120px] w-[90%] mx-auto font-figtree">
      <header
        className="bg-[#5a3a32] xl:h-[522px] rounded-[12px] sm:rounded-[16px] lg:rounded-[20px] text-white  px-4 sm:px-16 flex flex-col md:flex-row items-center relative justify-between mt-8 sm:mt-12 lg:mt-[160px] mb-8 sm:mb-16 lg:mb-[120px] "
        style={{
          backgroundImage: `url(${Bg})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      >
        {/* Left Side */}
        <div className="md:w-[50%] flex flex-col md:justify-start items-center md:items-start ">
          <div className="font-semibold text-[30px] md:text-[35px] lg:text-[45px] leading-[55px] md:leading-[60px] lg:leading-[90px]">
            <p>
              Talk To Flowkar <br /> We're <br />{" "}
            </p>
            <span className="relative">
              <p>All Ears</p>
              <img
                src={Circle}
                alt="Circle"
                className="absolute -top-[8px] md:-top-[7px] lg:-top-[5px] -left-[51px] lg:-left-[41px]"
              />
            </span>
          </div>
          <p className="mt-[30px] font-light text-[16px] md:text-[18px] lg:text-[28px] text-[#FFFFFF99] text-center md:text-left">
            Whether it's a query, feedback, or just a "Hi" - we're here and
            ready to respond fast.
          </p>
        </div>

        {/* Right Side */}
        <div className="">
          <img src={Dummy_01} alt="Image_01" className="xl:mb-[68px]" />
        </div>
      </header>
    </div>
  );
}

export default Hero;

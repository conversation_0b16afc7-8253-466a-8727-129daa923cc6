import { useState, useEffect } from "react";
import Bg from "../../assets/Blog/Blog_bg.svg";
import leftArrow from "../../assets/Navbar/leftArrow.svg";

// Placeholder image import (replace with your character image)
import Dummy_02 from "../../assets/ContactUs/Dummy_02.svg";

// MUI imports
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  IconButton,
  Typography,
  Box,
  createTheme,
  ThemeProvider,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { setApiMessage } from "../Toaster/setApiMessage";

// Create custom MUI theme to match website theme
const customTheme = createTheme({
  palette: {
    primary: {
      main: "#563D39",
      contrastText: "#ffffff",
    },
    secondary: {
      main: "#EFECEC",
    },
  },
  typography: {
    fontFamily: "Figtree, sans-serif",
  },
  components: {
    MuiDialog: {
      styleOverrides: {
        paper: {
          borderRadius: "16px",
          fontFamily: "Figtree, sans-serif",
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          "& .MuiOutlinedInput-root": {
            borderRadius: "8px",
            fontFamily: "Figtree, sans-serif",
            "&:hover fieldset": {
              borderColor: "#563D39",
            },
            "&.Mui-focused fieldset": {
              borderColor: "#563D39",
            },
          },
          "& .MuiOutlinedInput-input": {
            "&:-webkit-autofill": {
              WebkitBoxShadow: "0 0 0 1000px white inset",
              WebkitTextFillColor: "#000000",
              borderRadius: "8px",
            },
            "&:-webkit-autofill:hover": {
              WebkitBoxShadow: "0 0 0 1000px white inset",
              WebkitTextFillColor: "#000000",
            },
            "&:-webkit-autofill:focus": {
              WebkitBoxShadow: "0 0 0 1000px white inset",
              WebkitTextFillColor: "#000000",
            },
            "&:-webkit-autofill:active": {
              WebkitBoxShadow: "0 0 0 1000px white inset",
              WebkitTextFillColor: "#000000",
            },
          },
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: "8px",
          fontFamily: "Figtree, sans-serif",
          textTransform: "none",
          fontWeight: 500,
        },
      },
    },
  },
});

// Custom skeleton for Conversation section
const ConversationSkeleton = () => (
  <section className="w-[90%] mx-auto flex flex-col items-center py-12 animate-pulse">
    <div className="text-center mb-8">
      <div className="h-8 bg-[#563D39] bg-opacity-10 rounded w-64 mx-auto mb-4" />
      <div className="h-4 bg-[#563D39] bg-opacity-10 rounded w-96 mx-auto mb-8" />
    </div>
    <div className="relative w-full mx-auto flex flex-col md:flex-row items-stretch bg-[#EFECEC] rounded-[40px] shadow-lg overflow-visible md:px-4 py-8 md:py-8">
      {/* Left image skeleton */}
      <div className="relative flex-shrink-0 flex items-center justify-center md:w-[50%] w-full md:mb-0">
        <div className="bg-[#563D39] rounded-[40px] relative w-[320px] h-[370px] lg:w-[440px] lg:h-[460px] xl:-ml-16 z-10 flex items-end justify-center">
          <div className="absolute left-0 -top-[72px] lg:-top-[147px] w-[180px] h-[220px] lg:w-[260px] lg:h-[320px] bg-white bg-opacity-20 rounded-[40px]" />
        </div>
      </div>
      {/* Right card skeleton */}
      <div className="relative flex flex-col justify-center items-center md:items-start text-center md:text-left md:w-[50%] w-full pl-0 md:pl-12 md:pr-8 min-h-[460px]">
        <div className="h-4 bg-[#563D39] bg-opacity-10 rounded w-32 mb-4" />
        <div className="h-10 bg-[#563D39] bg-opacity-10 rounded w-2/3 mb-8" />
        <div className="bg-[#563D39] bg-opacity-10 rounded-[6px] w-[200px] h-[50px] mb-8" />
        <div className="space-y-2 w-full">
          <div className="h-6 bg-[#563D39] bg-opacity-10 rounded w-1/2 mx-auto mb-2" />
          <div className="h-6 bg-[#563D39] bg-opacity-10 rounded w-1/2 mx-auto mb-2" />
          <div className="h-6 bg-[#563D39] bg-opacity-10 rounded w-1/2 mx-auto" />
        </div>
      </div>
    </div>
  </section>
);

function Conversation() {
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    description: "",
  });
  const [errors, setErrors] = useState({
    name: "",
    email: "",
    description: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  const handleDialogOpen = () => {
    setDialogOpen(true);
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
    // Reset form data when closing
    setFormData({
      name: "",
      email: "",
      description: "",
    });
    // Reset errors when closing
    setErrors({
      name: "",
      email: "",
      description: "",
    });
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const handleSubmit = () => {
    // Reset errors
    setErrors({
      name: "",
      email: "",
      description: "",
    });

    let hasErrors = false;
    const newErrors = {};

    // Basic validation
    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
      hasErrors = true;
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
      hasErrors = true;
    } else {
      // Enhanced Email validation
      const email = formData.email.trim().toLowerCase();

      // Check for basic email format
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      if (!emailRegex.test(email)) {
        newErrors.email =
          "Please enter a valid email address (e.g., <EMAIL>)";
        hasErrors = true;
      } else if (
        email.startsWith(".") ||
        email.endsWith(".") ||
        email.includes("..")
      ) {
        newErrors.email =
          "Email cannot start/end with dots or contain consecutive dots";
        hasErrors = true;
      } else {
        // Check for valid domain
        const domain = email.split("@")[1];
        if (!domain || domain.length < 3 || !domain.includes(".")) {
          newErrors.email = "Please enter a valid email domain";
          hasErrors = true;
        }
      }
    }

    if (!formData.description.trim()) {
      newErrors.description = "Description is required";
      hasErrors = true;
    }

    if (hasErrors) {
      setErrors(newErrors);
      return;
    }

    // Enhanced Email validation for typos (only if basic validation passed)
    const email = formData.email.trim().toLowerCase();
    const domain = email.split("@")[1];

    // Check for common typos in popular domains
    const commonDomains = {
      "gmail.co": "gmail.com",
      "gmail.cm": "gmail.com",
      "gmial.com": "gmail.com",
      "yahoo.co": "yahoo.com",
      "yahoo.cm": "yahoo.com",
      "hotmail.co": "hotmail.com",
      "hotmail.cm": "hotmail.com",
      "outlook.co": "outlook.com",
      "outlook.cm": "outlook.com",
    };

    if (commonDomains[domain]) {
      const confirmed = window.confirm(
        `Did you mean ${formData.email.split("@")[0]}@${
          commonDomains[domain]
        }? Click OK to use the corrected email or Cancel to edit manually.`
      );
      if (confirmed) {
        setFormData((prev) => ({
          ...prev,
          email: `${formData.email.split("@")[0]}@${commonDomains[domain]}`,
        }));
        return;
      } else {
        return;
      }
    }

    // Start loading state
    setIsSubmitting(true);

    // Simulate API call with random delay (1-2 seconds)
    const randomDelay = Math.floor(Math.random() * 1000) + 1000; // 1000-2000ms

    setTimeout(() => {
      // Here you can add your form submission logic
      console.log("Form submitted:", formData);

      // Show success message and close dialog
      setApiMessage("success", "Thank you for your message!");
      setIsSubmitting(false);
      handleDialogClose();
    }, randomDelay);
  };

  if (loading) {
    return <ConversationSkeleton />;
  }

  return (
    <ThemeProvider theme={customTheme}>
      <section className="w-[90%] mx-auto flex flex-col items-center py-12">
        {/* Title and subtitle */}
        <div className="text-center mb-8 ">
          <h2 className="text-2xl md:text-3xl font-semibold text-[#563D39] mb-2">
            We're here to help.
          </h2>
          <p className="text-[#00000099] text-base md:text-lg font-normal mb-[50px] lg:mb-[100px]">
            Have questions, feedback, or need assistance? Our dedicated support
            team is just a message away and ready to assist you anytime with
            quick, friendly, and reliable help.
          </p>
        </div>
        {/* Card Section */}
        <div className="relative w-full mx-auto flex flex-col md:flex-row items-stretch bg-[#EFECEC] rounded-[40px] shadow-lg overflow-visible md:px-4 py-8 md:py-8">
          {/* Character Image (left) */}
          <div className="relative flex-shrink-0 flex items-center justify-center md:w-[50%] w-full  md:mb-0">
            <div
              className="bg-[#563D39] rounded-[40px] relative w-[320px] h-[370px] lg:w-[440px] lg:h-[460px] xl:-ml-16 z-10"
              style={{
                backgroundImage: `url(${Bg})`,
                backgroundSize: "cover",
                backgroundPosition: "center",
              }}
            >
              <img
                src={Dummy_02}
                alt="Character"
                className="absolute left-0 -top-[72px] lg:-top-[147px]"
              />
            </div>
          </div>
          {/* Conversation Card (right) */}
          <div className="relative flex flex-col justify-center items-center md:items-start text-center md:text-left md:w-[50%] w-full pl-0 md:pl-12 md:pr-8 min-h-[460px]">
            {/* Vertical Line */}
            <div
              className="hidden md:block absolute left-0 top-[90px] h-full"
              style={{ width: "16px" }}
            >
              {/* Brown part: from top to just below the heading */}
              <div
                style={{
                  background: "#563D39",
                  width: "2px",
                  height: "80px", // Adjust this value to match the height up to the heading
                  borderRadius: "2px",
                  marginLeft: "20px",
                }}
              />
              {/* Grey part: from below the heading to the bottom */}
              <div
                style={{
                  background: "#563D3933",
                  width: "2px",
                  height: "calc(100% - 230px)", // Adjust to fill the rest
                  borderRadius: "2px",
                  marginLeft: "20px",
                }}
              />
            </div>
            <p className="text-sm lg:text-base text-[#563D39] font-medium mb-4">
              Let's Talk. Your Idea Deserves a Great Start!
            </p>
            <h3 className="text-3xl  lg:text-6xl font-normal text-[#563D39] mb-8 leading-tight ">
              Start The Conversation
            </h3>
            <button
              onClick={handleDialogOpen}
              className="bg-[#563D39] text-white px-6 py-3 rounded-[6px] hover:bg-[#4a332f] transition-colors flex items-center justify-around space-x-2 w-[200px] h-[50px] pt-[8px] pr-[8px] pb-[8px] pl-[24px] mb-8"
            >
              <span className="text-base font-normal">Let's Connect</span>
              <img
                src={leftArrow}
                alt="Left Arrow"
                className="w-[34px] h-[34px]"
              />
            </button>
            <div className="text-[#563D3933] text-xl  lg:text-4xl font-normal leading-relaxed space-y-2">
              <div>You Imagine. We Build.</div>
              <div>Real Talk. Real Tech.</div>
              <div>Code Meets Vision.</div>
            </div>
          </div>
        </div>

        {/* MUI Dialog for Contact Form */}
        <Dialog
          open={dialogOpen}
          onClose={handleDialogClose}
          maxWidth="sm"
          fullWidth
          sx={{
            "& .MuiDialog-paper": {
              borderRadius: "16px",
              padding: "8px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "#563D39",
              fontWeight: 600,
              fontSize: "1.5rem",
              paddingBottom: "8px",
            }}
          >
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography variant="h6" component="div" sx={{ fontWeight: 600 }}>
                Let's Connect
              </Typography>
              <IconButton
                onClick={handleDialogClose}
                sx={{
                  color: "#563D39",
                  "&:hover": {
                    backgroundColor: "#563D3910",
                  },
                }}
              >
                <CloseIcon />
              </IconButton>
            </Box>
          </DialogTitle>

          <DialogContent sx={{ paddingTop: "16px !important" }}>
            <Typography
              variant="body2"
              sx={{
                color: "#00000099",
                marginBottom: "24px",
                fontSize: "0.95rem",
              }}
            >
              We'd love to hear from you! Please fill out the form below and
              we'll get back to you as soon as possible.
            </Typography>

            <TextField
              fullWidth
              label="Name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              margin="normal"
              variant="outlined"
              required
              error={!!errors.name}
              helperText={errors.name}
              sx={{
                marginBottom: "16px",
                "& .MuiFormHelperText-root": {
                  color: "#d32f2f",
                  fontSize: "0.875rem",
                  marginTop: "8px",
                  marginLeft: "0px",
                },
              }}
            />

            <TextField
              fullWidth
              label="Email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleInputChange}
              margin="normal"
              variant="outlined"
              required
              error={!!errors.email}
              helperText={errors.email}
              sx={{
                marginBottom: "16px",
                "& .MuiFormHelperText-root": {
                  color: "#d32f2f",
                  fontSize: "0.875rem",
                  marginTop: "8px",
                  marginLeft: "0px",
                },
              }}
            />

            <TextField
              fullWidth
              label="Description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              margin="normal"
              variant="outlined"
              multiline
              rows={4}
              required
              error={!!errors.description}
              helperText={errors.description}
              placeholder="Tell us about you or how we can help you..."
              sx={{
                "& .MuiFormHelperText-root": {
                  color: "#d32f2f",
                  fontSize: "0.875rem",
                  marginTop: "8px",
                  marginLeft: "0px",
                },
              }}
            />
          </DialogContent>

          <DialogActions sx={{ padding: "16px 24px 24px 24px" }}>
            <Button
              onClick={handleDialogClose}
              disabled={isSubmitting}
              sx={{
                color: "#563D39",
                "&:hover": {
                  backgroundColor: "#563D3910",
                },
                "&:disabled": {
                  color: "#563D3966",
                },
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              variant="contained"
              disabled={isSubmitting}
              sx={{
                backgroundColor: "#563D39",
                "&:hover": {
                  backgroundColor: "#4a332f",
                },
                "&:disabled": {
                  backgroundColor: "#563D3966",
                  color: "#ffffff99",
                },
                paddingX: "24px",
                minWidth: "140px",
                display: "flex",
                alignItems: "center",
                gap: "8px",
              }}
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Sending...
                </>
              ) : (
                "Send Message"
              )}
            </Button>
          </DialogActions>
        </Dialog>
      </section>
    </ThemeProvider>
  );
}

export default Conversation;

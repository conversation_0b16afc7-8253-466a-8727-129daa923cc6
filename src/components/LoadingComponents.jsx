import React from "react";

// Simple loading spinner
export const LoadingSpinner = () => (
  <div className="flex items-center justify-center py-12">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#563D39]"></div>
  </div>
);

// Skeleton loader for feature cards (ManagementFeatures)
export const FeatureCardsSkeleton = () => (
  <div className="py-16 px-4 w-[90%] mx-auto">
    <div className="text-center mb-12">
      <div className="h-8 bg-gray-200 rounded w-64 mx-auto mb-4 animate-pulse"></div>
      <div className="h-4 bg-gray-200 rounded w-96 mx-auto animate-pulse"></div>
    </div>
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {[...Array(6)].map((_, index) => (
        <div
          key={index}
          className="bg-gray-100 rounded-[20px] p-[20px] animate-pulse"
        >
          <div className="w-12 h-12 bg-gray-200 rounded mb-6"></div>
          <div className="h-6 bg-gray-200 rounded mb-3"></div>
          <div className="h-4 bg-gray-200 rounded mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
        </div>
      ))}
    </div>
  </div>
);

// Skeleton loader for social media icons
export const SocialMediaSkeleton = () => (
  <div className="py-16 px-4">
    <div className="text-center mb-12">
      <div className="h-8 bg-gray-200 rounded w-80 mx-auto mb-4 animate-pulse"></div>
      <div className="h-4 bg-gray-200 rounded w-96 mx-auto animate-pulse"></div>
    </div>
    <div className="flex justify-center items-center gap-4">
      {[...Array(8)].map((_, index) => (
        <div
          key={index}
          className="w-16 h-16 bg-gray-200 rounded-lg animate-pulse"
        ></div>
      ))}
    </div>
  </div>
);

// Skeleton loader for testimonials
export const TestimonialsSkeleton = () => (
  <div className="py-16 px-4">
    <div className="text-center mb-12">
      <div className="h-8 bg-gray-200 rounded w-64 mx-auto mb-4 animate-pulse"></div>
      <div className="h-4 bg-gray-200 rounded w-96 mx-auto animate-pulse"></div>
    </div>
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
      {[...Array(6)].map((_, index) => (
        <div
          key={index}
          className="bg-white rounded-lg p-6 shadow-sm animate-pulse"
        >
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 bg-gray-200 rounded-full mr-4"></div>
            <div>
              <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-16"></div>
            </div>
          </div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            <div className="h-4 bg-gray-200 rounded w-4/6"></div>
          </div>
        </div>
      ))}
    </div>
  </div>
);

// Skeleton loader for info cards
export const InfoCardSkeleton = () => (
  <div className="py-16 px-4">
    <div className="max-w-6xl mx-auto">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        {[...Array(4)].map((_, index) => (
          <div key={index} className="text-center animate-pulse">
            <div className="w-16 h-16 bg-gray-200 rounded-full mx-auto mb-4"></div>
            <div className="h-6 bg-gray-200 rounded w-24 mx-auto mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-32 mx-auto"></div>
          </div>
        ))}
      </div>
    </div>
  </div>
);

// Skeleton loader for about us section
export const AboutUsSkeleton = () => (
  <div className="w-full flex flex-col items-center">
    {/* Hero Section Skeleton */}
    <div className="px-4 sm:px-6 lg:px-6 mt-[80px] w-[90%] mx-auto font-figtree animate-pulse">
      <header className="bg-[#5a3a32] rounded-[20px] text-white py-12 px-4 flex flex-col gap-6 items-center relative overflow-hidden mt-12">
        <div className="h-10 bg-white bg-opacity-20 rounded w-2/3 mb-4"></div>
        <div className="h-6 bg-white bg-opacity-20 rounded w-1/2 mb-4"></div>
        <div className="h-10 bg-white bg-opacity-20 rounded w-40"></div>
      </header>
    </div>

    {/* Cards Section Skeleton */}
    <div className="w-full flex justify-center">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5 w-[90%] my-[60px] px-4 sm:px-6 lg:px-8">
        {[...Array(4)].map((_, idx) => (
          <div
            key={idx}
            className="flex flex-col items-center bg-gray-100 rounded-2xl px-6 py-8 w-full h-full text-center border animate-pulse"
          >
            <div className="mb-4 w-[61px] h-[60px] bg-gray-200 rounded"></div>
            <div className="h-6 bg-gray-200 rounded w-24 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-32 mb-2"></div>
          </div>
        ))}
      </div>
    </div>

    {/* Text and Image Section Skeleton */}
    <div className="bg-[#5C4742] rounded-[40px] flex flex-col md:flex-row items-center justify-center p-6 md:p-12 w-[90%] mx-auto my-8 min-h-[400px] font-figtree animate-pulse">
      <div className="flex-shrink-0 w-full md:w-1/2 flex justify-center items-center mb-8 md:mb-0 md:mr-8">
        <div className="rounded-[30px] object-cover w-[320px] h-[320px] md:w-[642px] md:h-[514px] bg-gray-300"></div>
      </div>
      <div className="w-full md:w-1/2 text-white flex flex-col justify-center space-y-4">
        <div className="h-10 bg-gray-200 rounded w-3/4 mb-4"></div>
        <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-5/6 mb-2"></div>
      </div>
    </div>

    {/* Stats Section Skeleton */}
    <section className="bg-[#563D391A] rounded-3xl py-12 px-4 w-[90%] mx-auto my-[60px] animate-pulse">
      <div className="flex flex-col md:flex-row justify-center items-center w-full mx-auto">
        {[...Array(3)].map((_, index) => (
          <div
            key={index}
            className="relative w-full md:flex-1 flex flex-col items-center text-center px-6 py-6"
          >
            <div className="h-10 bg-gray-200 rounded w-24 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-32"></div>
            {/* Dividers */}
            {index < 2 && (
              <div className="hidden md:block absolute right-0 top-1/2 transform -translate-y-1/2 h-[106px] w-px bg-gradient-to-b from-[#EFECEC] via-[#563D39] to-[#EFECEC]" />
            )}
            {index < 2 && (
              <div className="block md:hidden mt-6 w-[60%] h-px bg-gradient-to-r from-[#EFECEC] via-[#563D39] to-[#EFECEC]" />
            )}
          </div>
        ))}
      </div>
    </section>

    {/* Our Mission Section Skeleton */}
    <section className="w-[90%] mx-auto py-12 px-4 animate-pulse">
      <div className="h-8 bg-gray-200 rounded w-1/2 mx-auto mb-5"></div>
      <div className="h-4 bg-gray-200 rounded w-2/3 mx-auto mb-8"></div>
      <div className="flex flex-col gap-8">
        {[...Array(2)].map((_, idx) => (
          <div
            key={idx}
            className="flex flex-col md:flex-row items-center gap-6 md:gap-[60px] mb-4"
          >
            <div className="w-full md:w-[368px] h-[200px] md:h-[408px] bg-gray-200 rounded-[30px] mb-4 md:mb-0"></div>
            <div className="flex-1">
              <div className="h-6 bg-gray-200 rounded w-32 mb-3"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6 mb-2"></div>
            </div>
          </div>
        ))}
      </div>
    </section>

    {/* Get Started Section Skeleton */}
    <div className="px-4 sm:px-6 lg:px-0 my-[60px] w-[90%] mx-auto rounded-[40px] animate-pulse relative min-h-[400px] sm:min-h-[500px] lg:min-h-[600px] flex flex-col gap-[20px] sm:gap-[30px] lg:gap-[40px] text-center py-[100px] sm:py-[150px] lg:py-[220px] justify-center items-center">
      <div className="h-8 bg-white bg-opacity-20 rounded w-2/3 mx-auto mb-4"></div>
      <div className="h-10 bg-white bg-opacity-20 rounded w-40 mx-auto mb-4"></div>
      {/* Floating images skeletons */}
      <div className="absolute top-[20px] left-[20px] w-[80px] h-[80px] bg-gray-200 rounded-[12px]"></div>
      <div className="absolute top-[10px] right-[20px] w-[80px] h-[80px] bg-gray-200 rounded-[12px]"></div>
      <div className="absolute top-[170px] right-[10px] w-[100px] h-[100px] bg-gray-200 rounded-[12px]"></div>
      <div className="absolute bottom-[10px] right-[30px] w-[80px] h-[70px] bg-gray-200 rounded-[12px]"></div>
      <div className="absolute bottom-[20px] left-1/2 transform -translate-x-1/2 w-[70px] h-[70px] bg-gray-200 rounded-[12px]"></div>
      <div className="absolute bottom-[40px] left-[10px] w-[90px] h-[90px] bg-gray-200 rounded-[12px]"></div>
    </div>
  </div>
);

// Skeleton loader for thoughts section
export const ThoughtsSkeleton = () => (
  <div className="py-16 px-4">
    <div className="text-center mb-12">
      <div className="h-8 bg-gray-200 rounded w-48 mx-auto mb-4 animate-pulse"></div>
    </div>
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
      {[...Array(6)].map((_, index) => (
        <div key={index} className="bg-gray-50 rounded-lg p-6 animate-pulse">
          <div className="w-full h-48 bg-gray-200 rounded mb-4"></div>
          <div className="h-6 bg-gray-200 rounded mb-3"></div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          </div>
        </div>
      ))}
    </div>
  </div>
);

// Skeleton loader for FAQ section
export const FAQSkeleton = () => (
  <div className="py-16 px-4">
    <div className="text-center mb-12">
      <div className="h-8 bg-gray-200 rounded w-64 mx-auto mb-4 animate-pulse"></div>
    </div>
    <div className="max-w-4xl mx-auto space-y-4">
      {[...Array(5)].map((_, index) => (
        <div
          key={index}
          className="border border-gray-200 rounded-lg p-6 animate-pulse"
        >
          <div className="h-6 bg-gray-200 rounded w-3/4 mb-3"></div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
          </div>
        </div>
      ))}
    </div>
  </div>
);

// Skeleton loader for text reveal
export const TextRevealSkeleton = () => (
  <div className="py-16 px-4">
    <div className="max-w-4xl mx-auto text-center">
      <div className="space-y-4">
        <div className="h-8 bg-gray-200 rounded w-full animate-pulse"></div>
        <div className="h-8 bg-gray-200 rounded w-5/6 mx-auto animate-pulse"></div>
        <div className="h-8 bg-gray-200 rounded w-4/5 mx-auto animate-pulse"></div>
      </div>
    </div>
  </div>
);

// Comprehensive skeleton loader for blogs page
export const BlogsSkeleton = () => (
  <div className="min-h-screen px-4 sm:px-6 lg:px-6 mt-[80px] sm:mt-[80px] w-[90%] mx-auto font-figtree">
    {/* Header Section Skeleton */}
    <header className="bg-[#5a3a32] rounded-[12px] sm:rounded-[16px] lg:rounded-[20px] text-white py-8 sm:py-12 lg:py-[70px] px-4 sm:px-6 flex flex-col items-center relative overflow-hidden mt-8 sm:mt-12 lg:mt-[160px] mb-8 sm:mb-16 lg:mb-[120px]">
      <div className="h-8 bg-white bg-opacity-20 rounded w-80 mb-2 animate-pulse"></div>
      <div className="h-6 bg-white bg-opacity-20 rounded w-96 animate-pulse"></div>
    </header>

    {/* Featured Blog Section Skeleton */}
    <div className="flex flex-col xl:flex-row gap-6 lg:gap-8 items-start mb-8 sm:mb-12 lg:mb-[20px]">
      {/* Featured Image Skeleton */}
      <div className="w-full xl:flex-1">
        <div className="bg-gray-200 rounded-[12px] sm:rounded-[16px] lg:rounded-[20px] w-full h-[200px] sm:h-[250px] lg:h-[297px] animate-pulse"></div>
      </div>

      {/* Featured Content Skeleton */}
      <div className="w-full xl:w-96 xl:flex-shrink-0">
        <div className="flex flex-col">
          <div className="h-6 bg-gray-200 rounded mb-3 animate-pulse"></div>
          <div className="h-6 bg-gray-200 rounded mb-3 w-5/6 animate-pulse"></div>
          <div className="h-6 bg-gray-200 rounded mb-3 w-4/6 animate-pulse"></div>
          <div className="h-4 bg-gray-200 rounded mb-4 sm:mb-6 w-full animate-pulse"></div>
          <div className="h-4 bg-gray-200 rounded mb-4 sm:mb-6 w-3/4 animate-pulse"></div>
          <div className="flex items-center justify-between">
            <div className="h-3 bg-gray-200 rounded w-20 animate-pulse"></div>
            <div className="h-3 bg-gray-200 rounded w-16 animate-pulse"></div>
          </div>
        </div>
      </div>
    </div>

    {/* Main Content Grid Skeleton */}
    <div className="flex flex-col xl:flex-row gap-6 lg:gap-8 items-start w-full xl:items-stretch">
      {/* Blog Grid Skeleton */}
      <main className="w-full xl:flex-1">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 xl:pb-[120px] h-full">
          {Array.from({ length: 4 }).map((_, index) => (
            <div
              key={index}
              className="bg-white rounded-[16px] min-h-[280px] sm:min-h-[311px] w-full flex flex-col border-[1px] border-[#0000001A] shadow-sm"
            >
              <div className="rounded-tl-[16px] rounded-tr-[16px] mb-3 h-[160px] sm:h-[200px] w-full bg-gray-200 animate-pulse"></div>
              <div className="flex flex-col flex-1 px-3 pb-3">
                <div className="h-4 bg-gray-200 rounded mb-2 animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded mb-2 w-3/4 animate-pulse"></div>
                <div className="flex items-center justify-between mt-auto">
                  <div className="h-3 bg-gray-200 rounded w-16 animate-pulse"></div>
                  <div className="h-3 bg-gray-200 rounded w-20 animate-pulse"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </main>

      {/* Sidebar Skeleton */}
      <div className="w-full xl:w-96 xl:flex-shrink-0 xl:self-stretch mt-6 xl:mt-0">
        <div className="flex flex-col gap-4 sm:gap-6 h-full">
          {/* Search Skeleton */}
          <div className="bg-white rounded-[100px] shadow-sm border border-gray-100 p-4 flex items-center gap-3">
            <div className="w-5 h-5 bg-gray-200 rounded animate-pulse"></div>
            <div className="flex-1 h-4 bg-gray-200 rounded animate-pulse"></div>
          </div>

          {/* Recent Posts Skeleton */}
          <div className="bg-white rounded-xl p-4 flex flex-col flex-1 md:pb-[120px]">
            <div className="h-5 bg-gray-200 rounded w-24 mb-4 animate-pulse"></div>
            <div className="flex flex-col gap-4">
              {[1, 2, 3, 4].map((index) => (
                <div key={index} className="flex gap-3 items-start">
                  <div className="rounded-lg w-[130px] h-[120px] bg-gray-200 animate-pulse flex-shrink-0"></div>
                  <div className="flex-1 min-w-0">
                    <div className="h-3 bg-gray-200 rounded w-16 mb-2 animate-pulse"></div>
                    <div className="h-4 bg-gray-200 rounded mb-1 animate-pulse"></div>
                    <div className="h-4 bg-gray-200 rounded mb-1 w-3/4 animate-pulse"></div>
                    <div className="h-3 bg-gray-200 rounded w-12 animate-pulse"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
);

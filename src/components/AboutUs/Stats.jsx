import React from "react";

const statsData = [
  {
    value: "10,000+",
    label: "Posts managed monthly",
  },
  {
    value: "24/7",
    label: "Auto-posting across platforms",
  },
  {
    value: "70% faster",
    label: "Workflow than manual posting",
  },
];

function Stats() {
  return (
    <section className="bg-[#563D391A] rounded-3xl py-12 px-4 w-[90%] mx-auto my-[120px]">
      <div className="flex flex-col md:flex-row justify-center items-center w-full  mx-auto">
        {statsData.map((stat, index) => (
          <div
            key={index}
            className="relative w-full md:flex-1 flex flex-col items-center text-center px-6 py-6"
          >
            <span className="text-4xl md:text-5xl font-normal text-[#563D39]">
              {stat.value}
            </span>
            <span className="mt-4 text-lg md:text-xl text-[#563D3999] font-light">
              {stat.label}
            </span>

            {/* Vertical divider for desktop */}
            {index < statsData.length - 1 && (
              <div className="hidden md:block absolute right-0 top-1/2 transform -translate-y-1/2 h-[106px] w-px bg-gradient-to-b from-[#EFECEC] via-[#563D39] to-[#EFECEC]" />
            )}

            {/* Horizontal divider for mobile */}
            {index < statsData.length - 1 && (
              <div className="block md:hidden mt-6 w-[60%] h-px bg-gradient-to-r from-[#EFECEC] via-[#563D39] to-[#EFECEC]" />
            )}
          </div>
        ))}
      </div>
    </section>
  );
}

export default Stats;

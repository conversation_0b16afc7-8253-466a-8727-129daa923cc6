import React from "react";
import Bg from "../../assets/Blog/Blog_bg.svg";
import RightArrow from "../../assets/AboutUs/RightArrow.svg";

function Hero() {
  return (
    <div className="px-4 sm:px-6 lg:px-6 mt-[120px] sm:mt-[80px] w-[90%] mx-auto font-figtree">
      {/* Header Section */}
      <header
        className="bg-[#5a3a32] rounded-[12px] sm:rounded-[16px] lg:rounded-[20px] text-white py-8 sm:py-12 lg:py-[70px] px-4 sm:px-6 flex flex-col gap-[20px] items-center relative overflow-hidden mt-8 sm:mt-12 lg:mt-[160px]"
        style={{
          backgroundImage: `url(${Bg})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      >
        <h1 className="text-2xl sm:text-3xl lg:text-4xl font-semibold text-center leading-tight">
          Who We Are – The Flowkar Way
        </h1>
        <p className="text -[#FFFFFF99] text-base sm:text-lg lg:text-xl font-normal text-center opacity-80 max-w-2xl">
          Building Tomorrow's Technology Today
        </p>
        <button
          onClick={() => (window.location.href = "https://app.flowkar.com/")}
          className="bg-white text-black px-4 py-2 rounded-[6px] transition-colors flex items-center justify-around space-x-2 w-[201px] h-[42px] pt-[6px] pr-[6px] pb-[6px] pl-[20px] mt-[20px] font-normal"
        >
          <span>Discover Flowkar</span>
          <img
            src={RightArrow}
            alt="Right Arrow"
            className="w-[30px] h-[30px]"
          />
        </button>
      </header>
    </div>
  );
}

export default Hero;

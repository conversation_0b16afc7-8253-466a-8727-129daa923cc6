import { useEffect } from "react";

const ResourcePreloader = () => {
  useEffect(() => {
    // Add critical CSS for preventing layout shifts and white screens
    const criticalCSS = document.createElement("style");
    criticalCSS.textContent = `
      /* Prevent white screen flashes */
      body {
        background-color: white !important;
        min-height: 100vh;
      }
      
      /* Ensure hero section is always visible */
      .hero-section,
      [class*="hero"],
      [id*="hero"] {
        min-height: 100vh !important;
        background-color: white !important;
        opacity: 1 !important;
        visibility: visible !important;
      }
      
      /* Prevent layout shifts */
      #solutions-section {
        min-height: 600px !important;
        opacity: 1 !important;
        visibility: visible !important;
      }
      
      /* Ensure smooth transitions */
      .page-transition {
        transition: opacity 0.3s ease-in-out;
      }
      
      /* Optimize font loading */
      .font-figtree {
        font-display: swap;
      }
      
      /* Prevent component re-mounting white flashes */
      [data-reactroot] {
        min-height: 100vh;
        background-color: white;
      }
      
      /* Critical loading states */
      .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: white;
        z-index: 9999;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: opacity 0.3s ease-out;
      }
      
      .loading-overlay.fade-out {
        opacity: 0;
        pointer-events: none;
      }
    `;
    document.head.appendChild(criticalCSS);

    return () => {
      // Cleanup if needed
      try {
        if (criticalCSS.parentNode) {
          document.head.removeChild(criticalCSS);
        }
      } catch (error) {
        console.warn("Cleanup failed:", error);
      }
    };
  }, []);

  return null; // This component doesn't render anything
};

export default ResourcePreloader;

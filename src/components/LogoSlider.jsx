import React from 'react';
import Slider from 'react-slick';
import { useTranslation } from 'react-i18next'; // Importing useTranslation hook
import burger<PERSON>ogo from '../assets/burger.png';

const logos = [
    burgerLogo,
    'https://c8.alamy.com/comp/2K9M767/letter-q-logo-design-your-text-here-logo-template-on-white-background-2K9M767.jpg',
    'https://www.shutterstock.com/image-vector/creative-q-letter-logo-design-260nw-1636307575.jpg',
    'https://us.123rf.com/450wm/lumut/lumut1705/lumut170500174/77624244-c-letter-logo-template-vector-icon-design.jpg?ver=6',
    'https://us.123rf.com/450wm/findriyani/findriyani1812/findriyani181200677/*********-bird-wings-logo-vector.jpg?ver=6',
    'https://us.123rf.com/450wm/mhasanudinmarzuki/mhasanudinmarzuki2101/mhasanudinmarzuki210100241/*********-water-wave-logo-design-vector-template.jpg?ver=6',
    'https://us.123rf.com/450wm/saiful007/saiful0071707/saiful007170700952/82889257-sun-and-wave-logo-travel-logo-design-concept-template.jpg',
];

const LogoSlider = () => {
    const { t } = useTranslation(); // Using useTranslation to handle language

    const settings = {
        dots: false,
        infinite: true,
        speed: 500,
        slidesToShow: 6,
        slidesToScroll: 1,
        autoplay: true,
        autoplaySpeed: 1800,
        arrows: false,
        responsive: [
            { breakpoint: 1024, settings: { slidesToShow: 6 } },
            { breakpoint: 768, settings: { slidesToShow: 5 } },
            { breakpoint: 480, settings: { slidesToShow: 3 } },
            { breakpoint: 320, settings: { slidesToShow: 2 } },
        ],
    };

    return (
        <div className="py-12 bg-gray-100 dark:bg-gray-800">
            <div className="container mx-auto">
                <h2 className="text-center text-3xl font-semibold mb-10 text-gray-800 dark:text-white">
                    {t('partners.title')} {/* Dynamically using translation key */}
                </h2>
                <Slider {...settings}>
                    {logos.map((logo, index) => (
                        <div key={index} className="px-2">
                            <img
                                src={logo}
                                alt={`Partner Logo ${index + 1}`}
                                className="w-full h-24 lg:h-32 object-contain grayscale hover:grayscale-0 transition duration-300"
                            />
                        </div>
                    ))}
                </Slider>
            </div>
        </div>
    );
};

export default LogoSlider;

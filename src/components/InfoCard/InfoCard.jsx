import React from "react";
import LeftImage from "../../assets/InfoCard/LeftImage.svg";
import Icon_01 from "../../assets/InfoCard/Icon_01.svg";
import Icon_02 from "../../assets/InfoCard/Icon_02.svg";
import Icon_03 from "../../assets/InfoCard/Icon_03.svg";

function InfoCard() {
  const features = [
    {
      icon: (
        <img
          src={Icon_01}
          alt="Real-Time Tracking Icon"
          className="w-[70px] h-[70px]"
        />
      ),
      title: "Real-Time Comment, Like & DM Tracking",
      description:
        "Track user engagement in real time to respond instantly - every second counts when capturing attention and driving meaningful interactions.",
    },
    {
      icon: (
        <img
          src={Icon_02}
          alt="Video Insights Icon"
          className="w-[70px] h-[70px]"
        />
      ),
      title: "Reels And Short Video Insights",
      description:
        "Monitor views, reach, and engagement for every short video in real time - live insights give you clarity and control instantly.",
    },
    {
      icon: (
        <img
          src={Icon_03}
          alt="Team Collaboration Icon"
          className="w-[70px] h-[70px]"
        />
      ),
      title: "Team Collaboration & Approval Workflows",
      description:
        "Seamlessly collaborate with your team - review content, share feedback, and approve projects together in one unified, efficient workflow.",
    },
  ];

  return (
    <div className="py-16  w-[90%] mx-auto">
      <div className="mx-auto">
        <div
          className="rounded-[40px] p-8 lg:p-12"
          style={{
            background: "#563D39",
          }}
        >
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-stretch">
            {/* Left Side - Main Feature Card */}
            <div className="relative min-h-[400px] sm:min-h-[450px] lg:min-h-[500px]">
              <div className="rounded-[30px] p-6 lg:p-8 h-full">
                <div className="relative rounded-[20px] overflow-hidden h-full min-h-[350px] sm:min-h-[400px] lg:min-h-[450px]">
                  <img
                    src={LeftImage}
                    alt="Left Image"
                    className="w-full h-full object-cover rounded-[20px]"
                  />
                  {/* Blur overlay at bottom */}
                  <div className="absolute bottom-0 left-0 right-0 h-32 backdrop-blur-sm bg-gradient-to-t from-black/80 via-black/40 to-transparent pointer-events-none rounded-b-[20px]"></div>
                  {/* Title positioned on blur */}
                  <div className="absolute bottom-0 left-0 right-0 p-4 sm:p-6">
                    <h2 className="text-white text-lg sm:text-xl lg:text-[30px] font-semibold leading-tight">
                      Unified Dashboard For Instagram, Facebook, LinkedIn,
                      YouTube, And More
                    </h2>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Side - Feature List */}
            <div className="space-y-8 sm:space-y-12 lg:space-y-[70px] flex flex-col justify-center h-full py-4 lg:py-0">
              {features.map((feature, index) => (
                <div
                  key={index}
                  className="flex items-start space-x-4 sm:space-x-6"
                >
                  {/* Icon Container */}
                  <div className="flex-shrink-0 w-[70px] h-[70px] sm:w-[80px] sm:h-[80px] rounded-lg flex items-center justify-center">
                    {feature.icon}
                  </div>

                  {/* Content */}
                  <div className="flex-1">
                    <h3 className="text-white text-base sm:text-lg lg:text-xl font-semibold mb-2 leading-tight">
                      {feature.title}
                    </h3>
                    <p className="text-[#FFFFFF99] text-sm lg:text-base leading-relaxed">
                      {feature.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default InfoCard;

import React from "react";

const Features = () => {
  return (
    <div className="min-h-screen bg-white dark:bg-darkBlack pt-20">
      <div className="max-w-[1200px] mx-auto px-4 py-12">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-black dark:text-white mb-8">
            Features
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 mb-12">
            Discover the powerful features that make <PERSON><PERSON> the perfect social media management tool.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-gray-50 dark:bg-darkGrayCard p-6 rounded-lg">
              <h3 className="text-xl font-semibold text-black dark:text-white mb-4">
                Social Media Management
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Manage all your social media accounts from one centralized dashboard.
              </p>
            </div>
            
            <div className="bg-gray-50 dark:bg-darkGrayCard p-6 rounded-lg">
              <h3 className="text-xl font-semibold text-black dark:text-white mb-4">
                Analytics & Insights
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Get detailed analytics and insights to optimize your social media strategy.
              </p>
            </div>
            
            <div className="bg-gray-50 dark:bg-darkGrayCard p-6 rounded-lg">
              <h3 className="text-xl font-semibold text-black dark:text-white mb-4">
                Content Scheduling
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Schedule your posts in advance and maintain consistent engagement.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Features;

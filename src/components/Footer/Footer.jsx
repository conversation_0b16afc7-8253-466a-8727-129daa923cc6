import React from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import logo from "../../assets/logo.svg";

const Footer = () => {
  return (
    <motion.footer
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="w-[90%]  py-[30px] mx-auto rounded-[30px] "
    >
      {/* Main Footer Content */}
      <div className="bg-[#563D391A] rounded-tl-[30px] rounded-tr-[30px] mx-auto px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center md:text-left">
          {/* Flowkar Section */}
          <div className="space-y-4 pr-4 md:pr-8 lg:pr-12 flex flex-col items-center md:items-start">
            <div className="font-bold text-[#563D39] mb-4">
              <img src={logo} alt="Flowkar" className="w-32 h-auto" />
            </div>
            <p className="text-[14px] md:text-[16px] font-medium text-[#00000099] leading-[28px] max-w-[250px] md:max-w-[280px] lg:max-w-[320px]">
              Flowkar lets you create, schedule, and share content across
              platforms - helping you grow your audience with ease and
              efficiency.
            </p>
          </div>

          {/* Contact Us Section */}
          <div className="space-y-4 flex flex-col items-center md:items-start">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">
              Contact Us
            </h3>
            <div className="space-y-2">
              <p className="text-gray-700 text-[16px]">
                We are always happy to assist you
              </p>
              <a
                href="mailto:<EMAIL>"
                className="text-gray-700 text-sm "
              >
                Email:{" "}
                <span className="hover:underline"> <EMAIL> </span>
              </a>
            </div>
          </div>

          {/* Check it out Section */}
          <div className="space-y-4 flex flex-col items-center md:items-start">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">
              Check it out
            </h3>
            <ul className="space-y-2">
              <li>
                <Link
                  to="/"
                  className="text-gray-700 text-sm hover:text-[#563D39] transition-colors hover:underline"
                >
                  Home
                </Link>
              </li>
              <li>
                <Link
                  to="/solutions"
                  className="text-gray-700 text-sm hover:text-[#563D39] transition-colors hover:underline"
                >
                  Solutions
                </Link>
              </li>
              <li>
                <Link
                  to="/aboutus"
                  className="text-gray-700 text-sm hover:text-[#563D39] transition-colors hover:underline"
                >
                  About Us
                </Link>
              </li>
              <li>
                <Link
                  to="/blogs"
                  className="text-gray-700 text-sm hover:text-[#563D39] transition-colors hover:underline"
                >
                  Blog
                </Link>
              </li>
              {/* <li>
                <Link
                  to="/privacy-policy"
                  className="text-gray-700 text-sm hover:text-[#563D39] transition-colors hover:underline"
                >
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link
                  to="/terms"
                  className="text-gray-700 text-sm hover:text-[#563D39] transition-colors hover:underline"
                >
                  Terms & Condition
                </Link>
              </li> */}
              <li>
                <Link
                  to="/contact-us"
                  className="text-gray-700 text-sm hover:text-[#563D39] transition-colors hover:underline"
                >
                  Contact Us
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Copyright Text */}
        {/* <div className="mt-8 pt-6 border-t border-gray-300 text-center ">
          <p className="text-xs text-gray-600 leading-relaxed">
            By using Flowkar, you agree to our Terms of Service, Privacy Policy
            and Data Handling Policy. Some links or features may include
            affiliate partnerships or third-party services. Flowkar is committed
            to providing a secure, scalable experience for creators and
            businesses. Features and content are subject to change without prior
            notice.
          </p>
        </div> */}
      </div>

      {/* Bottom Dark Section */}
      <div className="bg-[#563D39] py-4 rounded-bl-[30px] rounded-br-[30px] ">
        <div className="mx-auto px-8 flex flex-col sm:flex-row justify-between items-center text-center sm:text-left">
          <p className="text-white text-sm">
            © 2025 Flowkar, Inc. - All Rights Reserved
          </p>
          <div className="flex space-x-6 mt-2 sm:mt-0 justify-center sm:justify-end">
            <Link
              to="/terms"
              className="text-white text-sm hover:text-gray-300 transition-colors"
            >
              Terms of Use
            </Link>
            <Link
              to="/privacy-policy"
              className="text-white text-sm hover:text-gray-300 transition-colors"
            >
              Privacy Policy
            </Link>
          </div>
        </div>
      </div>
    </motion.footer>
  );
};

export default Footer;

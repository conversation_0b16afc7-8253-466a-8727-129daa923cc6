import React from 'react';
import { useTranslation } from 'react-i18next';
import background3 from '../assets/background3.png';
import vectorImage from '../assets/number.svg';  // Import the vector image

const Number = () => {
    const { t } = useTranslation();



    return (
        <div
            className="relative min-h-screen text-white overflow-hidden flex flex-col justify-center"
            style={{
                backgroundImage: `url(${background3})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat',
            }}
        >  {/* Floating shapes background */}
            <div className="absolute inset-0 opacity-10">
                {[...Array(20)].map((_, i) => (
                    <div
                        key={i}
                        className="absolute rounded-full bg-black"
                        style={{
                            width: Math.random() * 20 + 10 + 'px',
                            height: Math.random() * 20 + 10 + 'px',
                            left: Math.random() * 100 + '%',
                            top: Math.random() * 100 + '%',
                            transform: 'translate(-50%, -50%)',
                            animation: `float ${Math.random() * 10 + 20}s linear infinite`
                        }}
                    />
                ))}
            </div>

            {/* Main content */}
            <div className="relative z-10 max-w-6xl mx-auto p-8">
                <div className="grid md:flex md:flex-row-reverse  items-center xl:gap-20 lg:gap-16 sm:gap-10 gap-2 ">
                    {/* Right side illustration (Text content) */}
                    <div className="space-y-8 flex flex-col md:w-1/2">
                        <div className="space-y-2">
                            <h1 className=" text-2xl sm:text-3xl  lg:text-5xl md:text-4xl font-light text-[#674941] ">
                                {t('number.businessSoftware')}
                            </h1>

                        </div>

                        <div className=" gap-8">

                            <div className="space-y-2 backdrop-blur-sm  rounded-lg">
                                <div className="text-xl  text-[#674941] ">
                                    At Flowkar, we’re shaping the future of business with passion and expertise. Trusted globally, we deliver innovative solutions that drive success.
                                </div>
                            </div>


                        </div>
                    </div>

                    {/* Left side (Vector Image) */}
                    <div className="relative flex items-center justify-center md:w-1/2">
                        <div className="w-full flex justify-center md:justify-start ">
                            <img src={vectorImage} alt="Vector Illustration" className="w-full h-1/2 sm:h-full " />
                        </div>
                    </div>
                </div>
            </div>

            {/* Add the animation keyframes */}
            <style>
                {`
          @keyframes float {
            0% { transform: translate(-50%, -50%) rotate(0deg) translateX(0px); }
            50% { transform: translate(-50%, -50%) rotate(180deg) translateX(50px); }
            100% { transform: translate(-50%, -50%) rotate(360deg) translateX(0px); }
          }
        `}
            </style>
        </div>
    );
};

export default Number;

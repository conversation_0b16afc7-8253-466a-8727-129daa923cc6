import React, { useEffect, useRef, useState, useCallback } from "react";
import Iphone from "../../assets/TextReveal/Iphone.svg";

// Line Component
const TextRevealLine = ({ children, isActive, className }) => {
  return (
    <div
      className={`transition-all duration-700 ease-out ${className} ${
        isActive
          ? "opacity-100 transform translate-y-0"
          : "opacity-30 transform translate-y-4"
      }`}
    >
      {children}
    </div>
  );
};

const handleRedirectPlayStore = () => {
  window.location.href =
    "https://play.google.com/store/apps/details?id=com.app.flowkar&hl=en";
};

const handleRedirectAppStore = () => {
  window.location.href = "https://apps.apple.com/in/app/flowkar/id6740058663";
};

// Reveal Component
const TextRevealComponent = ({ lines, className = "" }) => {
  const [activeLines, setActiveLines] = useState(new Set());
  const [animationCompleted, setAnimationCompleted] = useState(false);
  const containerRef = useRef(null);

  useEffect(() => {
    const handleScroll = () => {
      // If animation is already completed, don't do anything
      if (animationCompleted) return;

      if (!containerRef.current) return;
      const rect = containerRef.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;

      // Improved scroll calculation - responsive trigger points based on screen size
      const elementTop = rect.top;
      const isMobile = windowHeight < 768; // Mobile breakpoint
      const triggerPoint = isMobile ? windowHeight * 0.6 : windowHeight * 0.5;
      const endPoint = isMobile ? windowHeight * 0.2 : windowHeight * 0.1;

      let scrollProgress = 0;
      if (elementTop < triggerPoint) {
        const scrollDistance = triggerPoint - endPoint;
        const currentScroll = triggerPoint - elementTop;
        scrollProgress = Math.max(
          0,
          Math.min(1, currentScroll / scrollDistance)
        );
      }

      // Calculate how many lines should be active based on scroll progress
      const activeCount = Math.floor(scrollProgress * lines.length);
      const newActiveLines = new Set(
        Array.from({ length: activeCount }, (_, i) => i)
      );
      setActiveLines(newActiveLines);

      // Check if all lines are now active (animation completed)
      if (activeCount >= lines.length) {
        setAnimationCompleted(true);
        // Remove scroll listener since animation is complete
        window.removeEventListener("scroll", handleScroll);
      }
    };

    window.addEventListener("scroll", handleScroll);
    handleScroll();

    // Cleanup function
    return () => window.removeEventListener("scroll", handleScroll);
  }, [lines.length, animationCompleted]);

  return (
    <div ref={containerRef} className={className}>
      <div className="space-y-3 xs:space-y-4 sm:space-y-5 md:space-y-6 max-w-xs xs:max-w-sm sm:max-w-md md:max-w-lg lg:max-w-xl xl:max-w-2xl 2xl:max-w-4xl text-center md:text-left">
        {lines.map(({ text, className }, lineIdx) => {
          const isActive = activeLines.has(lineIdx);
          return (
            <TextRevealLine
              key={lineIdx}
              isActive={isActive}
              className={className}
            >
              {text}
            </TextRevealLine>
          );
        })}
      </div>
    </div>
  );
};

const PhoneMockup = () => {
  return (
    <div className="relative w-full max-w-[280px] xs:max-w-[320px] sm:max-w-[360px] md:max-w-[380px] lg:max-w-[420px] mx-auto">
      <img src={Iphone} alt="phone" className="w-full h-auto" />
    </div>
  );
};

const AppStoreBadges = () => (
  <div className="flex flex-col xs:flex-row gap-3 sm:gap-4 mt-8 sm:mt-12 items-center md:items-start justify-center md:justify-start">
    {/* App Store Button */}
    <div
      className="bg-[#FFFFFF] backdrop-blur-sm rounded-[6px] w-full xs:w-[150px] sm:w-[163px] h-[42px] px-3 sm:px-5 py-1.5 flex items-center justify-between hover:bg-white transition-all duration-300 cursor-pointer shadow-lg"
      onClick={handleRedirectAppStore}
    >
      <div className="text-left flex-1">
        <div className="text-black text-[14px] sm:text-[16px] font-normal">
          App Store
        </div>
      </div>
      <div className="w-[26px] h-[26px] sm:w-[30px] sm:h-[30px] bg-[#563D3933] rounded-[4px] flex items-center justify-center ml-2 sm:ml-4 flex-shrink-0">
        {/* Placeholder Apple icon */}
        <svg
          className="w-4 h-4 sm:w-6 sm:h-6 text-black"
          fill="currentColor"
          viewBox="0 0 24 24"
        >
          <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z" />
        </svg>
      </div>
    </div>

    {/* Google Play Button */}
    <div
      className="bg-[#FFFFFF] backdrop-blur-sm rounded-[6px] w-full xs:w-[150px] sm:w-[163px] h-[42px] px-3 sm:px-5 py-1.5 flex items-center justify-between hover:bg-white transition-all duration-300 cursor-pointer shadow-lg"
      onClick={handleRedirectPlayStore}
    >
      <div className="text-left flex-1">
        <div className="text-black text-[14px] sm:text-[16px] font-normal whitespace-nowrap">
          Google Play
        </div>
      </div>
      <div className="w-[26px] h-[26px] sm:w-[30px] sm:h-[30px] bg-[#563D3933] rounded-[4px] flex items-center justify-center ml-2 sm:ml-4 flex-shrink-0">
        {/* Placeholder Google Play icon */}
        <svg
          className="w-4 h-4 sm:w-6 sm:h-6 text-black"
          fill="currentColor"
          viewBox="0 0 24 24"
        >
          <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.61 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z" />
        </svg>
      </div>
    </div>
  </div>
);

function TextReveal() {
  const revealLines = [
    {
      text: "One platform",
      className:
        "text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-medium leading-tight bg-gradient-to-r from-white to-gray-300 bg-clip-text",
    },
    {
      text: "Every story No chaos",
      className:
        "text-xl xs:text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-medium leading-tight bg-gradient-to-r from-white to-gray-300 bg-clip-text",
    },
    {
      text: "They juggled tools,",
      className:
        "text-lg xs:text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-medium leading-relaxed",
    },
    {
      text: "Tabs, and time",
      className:
        "text-lg xs:text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-medium leading-relaxed",
    },
    {
      text: "Just to post one story",
      className:
        "text-lg xs:text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-medium leading-relaxed",
    },
    {
      text: "Flowkar changed the game",
      className:
        "text-lg xs:text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-medium leading-relaxed bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text",
    },
    {
      text: "Syncing every platform into one",
      className:
        "text-lg xs:text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-medium leading-relaxed",
    },
    {
      text: "Now, creators create",
      className:
        "text-lg xs:text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-medium leading-relaxed",
    },
    {
      text: "And Flowkar",
      className:
        "text-lg xs:text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-medium leading-relaxed bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text",
    },
    {
      text: "takes care of",
      className:
        "text-xl xs:text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-medium leading-tight bg-gradient-to-r from-white to-gray-300 bg-clip-text",
    },
    {
      text: "the rest.",
      className:
        "text-xl xs:text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-medium leading-tight bg-gradient-to-r from-white to-gray-300 bg-clip-text",
    },
  ];

  return (
    <section className="relative bg-[#563D39] rounded-2xl sm:rounded-3xl p-4 xs:p-6 sm:p-8 md:p-12 lg:p-16 text-white flex flex-col md:flex-row items-center justify-between w-[90%] mx-auto min-h-screen overflow-hidden">
      {/* Header */}
      <div className="absolute top-3 xs:top-4 sm:top-6 left-3 xs:left-4 sm:left-6 right-3 xs:right-4 sm:right-6 flex flex-col xs:flex-row justify-between items-start xs:items-center z-10 gap-2 xs:gap-0">
        <h1 className="text-white text-lg xs:text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl 2xl:text-6xl font-medium">
          Everything
        </h1>
        <p className="text-orange-200 text-base xs:text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl font-light">
          a team needs
        </p>
      </div>

      {/* Content */}
      <div className="w-full md:w-1/2 lg:w-[55%] relative z-10 flex flex-col items-center md:items-start">
        <div className="mt-16 xs:mt-18 sm:mt-20 md:mt-24 lg:mt-28 xl:mt-32">
          <TextRevealComponent lines={revealLines} />
          <AppStoreBadges />
        </div>
      </div>

      {/* Phone mockup */}
      <div className="w-full mt-12 xs:mt-14 sm:mt-16 md:mt-0 md:w-1/2 lg:w-[45%] flex justify-center items-center min-h-full relative z-10">
        <PhoneMockup />
      </div>

      {/* Bottom gradient overlay */}
      <div className="absolute bottom-0 left-0 right-0 h-24 sm:h-32 bg-gradient-to-t from-black/20 to-transparent"></div>
    </section>
  );
}

export default TextReveal;

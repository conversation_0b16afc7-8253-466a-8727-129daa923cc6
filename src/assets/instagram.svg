<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 27.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 74 74" style="enable-background:new 0 0 74 74;" xml:space="preserve">
<style type="text/css">
	.st0{clip-path:url(#SVGID_00000121962438110785389370000018338655690248991666_);}
	.st1{fill:url(#SVGID_00000098206770803767039110000013617704890230218368_);}
	.st2{fill:url(#SVGID_00000009561558616229887350000003719885296520519080_);}
	.st3{fill:#FFFFFF;}
	.st4{fill:#1977F3;}
	.st5{fill:#1D9BF0;}
	.st6{fill:#FF004F;}
	.st7{fill:#00F2EA;}
	.st8{fill:#D8D8D8;}
	.st9{fill:#34A853;}
	.st10{fill:#FBBC04;}
	.st11{fill:#EA4335;}
	.st12{fill:#4285F4;}
	.st13{fill:#9049FD;}
	.st14{fill:#FDFCFE;}
	.st15{fill:#3C8BD9;}
	.st16{fill:#FABC04;}
	.st17{fill:#34A852;}
	.st18{fill:#E1C025;}
	.st19{fill:#757575;}
	.st20{fill:#3C4BA6;}
	.st21{fill:#FF0000;}
	.st22{fill:#CA2127;}
	.st23{fill:#4989F5;}
	.st24{fill:url(#SVGID_00000045605775763950364020000016602038795732074380_);}
	.st25{fill:#7BABF7;}
	.st26{fill:#3F51B5;}
	.st27{fill-rule:evenodd;clip-rule:evenodd;fill:#709BE0;}
	.st28{fill-rule:evenodd;clip-rule:evenodd;fill:#3C4BA6;}
	.st29{enable-background:new    ;}
	.st30{fill:#EE1D52;}
	.st31{fill:#69C9D0;}
</style>
<g>
	<defs>
		<circle id="SVGID_1_" cx="37" cy="37" r="37"/>
	</defs>
	<clipPath id="SVGID_00000046302342117624287580000005026841351789799311_">
		<use xlink:href="#SVGID_1_"  style="overflow:visible;"/>
	</clipPath>
	<g style="clip-path:url(#SVGID_00000046302342117624287580000005026841351789799311_);">
		
			<radialGradient id="SVGID_00000038378741270647607460000014619852327696545973_" cx="95.752" cy="-545.5112" r="37.0031" gradientTransform="matrix(0 -1.982 -1.8439 0 -986.2164 269.478)" gradientUnits="userSpaceOnUse">
			<stop  offset="0" style="stop-color:#FFDD55"/>
			<stop  offset="0.1" style="stop-color:#FFDD55"/>
			<stop  offset="0.5" style="stop-color:#FF543E"/>
			<stop  offset="1" style="stop-color:#C837AB"/>
		</radialGradient>
		<path style="fill:url(#SVGID_00000038378741270647607460000014619852327696545973_);" d="M37.01,0C21.56,0,17.04,0.02,16.16,0.09
			c-3.17,0.26-5.14,0.76-7.29,1.83C7.21,2.74,5.91,3.7,4.62,5.03c-2.35,2.44-3.77,5.44-4.29,9c-0.25,1.73-0.32,2.08-0.34,10.92
			c-0.01,2.95,0,6.82,0,12.03c0,15.44,0.02,19.95,0.09,20.83c0.26,3.09,0.74,5.03,1.76,7.15c1.96,4.06,5.7,7.12,10.1,8.25
			c1.53,0.39,3.21,0.61,5.37,0.71C18.24,73.97,27.59,74,36.93,74s18.7-0.01,19.59-0.06c2.5-0.12,3.96-0.31,5.57-0.73
			c4.43-1.14,8.11-4.15,10.1-8.27c1-2.07,1.51-4.09,1.74-7.01c0.05-0.64,0.07-10.8,0.07-20.96c0-10.15-0.02-20.3-0.07-20.94
			c-0.23-2.97-0.74-4.97-1.78-7.08c-0.85-1.73-1.8-3.02-3.17-4.34c-2.45-2.34-5.44-3.76-9.01-4.28C58.25,0.09,57.9,0.02,49.06,0
			H37.01z"/>
		
			<radialGradient id="SVGID_00000041974587411768441920000002335908004731078281_" cx="69.6158" cy="-370.6219" r="37.0031" gradientTransform="matrix(0.1739 0.8687 3.5818 -0.7172 1302.9757 -320.9485)" gradientUnits="userSpaceOnUse">
			<stop  offset="0" style="stop-color:#3771C8"/>
			<stop  offset="0.128" style="stop-color:#3771C8"/>
			<stop  offset="1" style="stop-color:#6600FF;stop-opacity:0"/>
		</radialGradient>
		<path style="fill:url(#SVGID_00000041974587411768441920000002335908004731078281_);" d="M37.01,0C21.56,0,17.04,0.02,16.16,0.09
			c-3.17,0.26-5.14,0.76-7.29,1.83C7.21,2.74,5.91,3.7,4.62,5.03c-2.35,2.44-3.77,5.44-4.29,9c-0.25,1.73-0.32,2.08-0.34,10.92
			c-0.01,2.95,0,6.82,0,12.03c0,15.44,0.02,19.95,0.09,20.83c0.26,3.09,0.74,5.03,1.76,7.15c1.96,4.06,5.7,7.12,10.1,8.25
			c1.53,0.39,3.21,0.61,5.37,0.71C18.24,73.97,27.59,74,36.93,74s18.7-0.01,19.59-0.06c2.5-0.12,3.96-0.31,5.57-0.73
			c4.43-1.14,8.11-4.15,10.1-8.27c1-2.07,1.51-4.09,1.74-7.01c0.05-0.64,0.07-10.8,0.07-20.96c0-10.15-0.02-20.3-0.07-20.94
			c-0.23-2.97-0.74-4.97-1.78-7.08c-0.85-1.73-1.8-3.02-3.17-4.34c-2.45-2.34-5.44-3.76-9.01-4.28C58.25,0.09,57.9,0.02,49.06,0
			H37.01z"/>
		<path class="st3" d="M37,9.68c-7.42,0-8.35,0.03-11.27,0.17c-2.91,0.13-4.89,0.59-6.63,1.27c-1.8,0.7-3.32,1.63-4.84,3.15
			c-1.52,1.52-2.45,3.04-3.15,4.84c-0.68,1.74-1.14,3.72-1.27,6.63C9.7,28.65,9.67,29.58,9.67,37s0.03,8.35,0.17,11.26
			c0.13,2.91,0.59,4.89,1.27,6.63c0.7,1.8,1.63,3.32,3.15,4.84c1.52,1.52,3.04,2.46,4.84,3.15c1.74,0.68,3.72,1.14,6.63,1.27
			c2.91,0.13,3.85,0.17,11.27,0.17c7.42,0,8.35-0.03,11.26-0.17c2.91-0.13,4.9-0.59,6.64-1.27c1.8-0.7,3.32-1.63,4.84-3.15
			c1.52-1.52,2.45-3.04,3.15-4.84c0.67-1.74,1.13-3.72,1.27-6.63c0.13-2.91,0.17-3.84,0.17-11.26s-0.03-8.35-0.17-11.27
			c-0.14-2.91-0.6-4.89-1.27-6.63c-0.7-1.8-1.63-3.32-3.15-4.84c-1.52-1.52-3.04-2.45-4.84-3.15c-1.74-0.68-3.73-1.14-6.64-1.27
			C45.34,9.71,44.41,9.68,37,9.68L37,9.68z M34.54,14.6c0.73,0,1.54,0,2.45,0c7.3,0,8.16,0.03,11.04,0.16
			c2.66,0.12,4.11,0.57,5.07,0.94c1.28,0.5,2.18,1.09,3.14,2.04c0.96,0.96,1.55,1.87,2.04,3.14c0.37,0.96,0.82,2.41,0.94,5.07
			C59.37,28.84,59.4,29.7,59.4,37s-0.03,8.16-0.16,11.04c-0.12,2.66-0.57,4.11-0.94,5.07c-0.5,1.28-1.09,2.18-2.04,3.14
			c-0.96,0.96-1.86,1.55-3.14,2.04c-0.96,0.38-2.41,0.82-5.07,0.94c-2.88,0.13-3.75,0.16-11.04,0.16c-7.3,0-8.16-0.03-11.04-0.16
			c-2.66-0.12-4.11-0.57-5.07-0.94c-1.28-0.5-2.19-1.09-3.14-2.04c-0.96-0.96-1.55-1.86-2.04-3.14c-0.37-0.96-0.82-2.41-0.94-5.07
			c-0.13-2.88-0.16-3.75-0.16-11.04s0.03-8.16,0.16-11.04c0.12-2.66,0.57-4.11,0.94-5.07c0.5-1.28,1.09-2.19,2.04-3.14
			c0.96-0.96,1.87-1.55,3.14-2.04c0.96-0.38,2.41-0.82,5.07-0.94C28.47,14.64,29.45,14.6,34.54,14.6L34.54,14.6z M51.58,19.14
			c-1.81,0-3.28,1.47-3.28,3.28c0,1.81,1.47,3.28,3.28,3.28s3.28-1.47,3.28-3.28C54.86,20.61,53.39,19.14,51.58,19.14L51.58,19.14z
			 M37,22.97c-7.75,0-14.03,6.28-14.03,14.03S29.25,51.03,37,51.03c7.75,0,14.03-6.28,14.03-14.03S44.74,22.97,37,22.97L37,22.97z
			 M37,27.89c5.03,0,9.11,4.08,9.11,9.11c0,5.03-4.08,9.11-9.11,9.11c-5.03,0-9.11-4.08-9.11-9.11C27.89,31.97,31.96,27.89,37,27.89
			z"/>
	</g>
</g>
</svg>

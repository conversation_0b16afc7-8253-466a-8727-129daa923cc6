<svg width="584" height="184" viewBox="0 0 584 184" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_458_12891)">
<rect x="0.332031" y="0.529785" width="583.333" height="182.778" rx="91.3889" fill="url(#paint0_linear_458_12891)" fill-opacity="0.3"/>
<rect x="0.818142" y="1.0159" width="582.361" height="181.806" rx="90.9028" stroke="black" stroke-opacity="0.1" stroke-width="0.972222"/>
</g>
<defs>
<filter id="filter0_i_458_12891" x="-9.39019" y="-9.19244" width="602.778" height="202.222" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.45833"/>
<feGaussianBlur stdDeviation="0.486111"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_458_12891"/>
</filter>
<linearGradient id="paint0_linear_458_12891" x1="197.693" y1="39.9048" x2="380.471" y2="39.9048" gradientUnits="userSpaceOnUse">
<stop stop-opacity="0.05"/>
<stop offset="0.5" stop-opacity="0.1"/>
<stop offset="1" stop-opacity="0.05"/>
</linearGradient>
</defs>
</svg>

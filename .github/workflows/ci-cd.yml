
name: CI/CD Pipeline

on:
  push:
    branches: [ staging ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up SSH
      uses: webfactory/ssh-agent@v0.9.0
      with:
        ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

    - name: Deploy to Ubuntu server
      run: |
        ssh -T -o StrictHostKeyChecking=no flexion@************* << 'EOF'  
          set -e
          echo "Starting deployment..."
          cd /var/www/flowkar/private/staging/www/CFAW-Flowkar-Web
          echo "Pulling latest changes..."
          sudo ../pull.sh
          echo "Installing dependencies..."
          sudo npm i -f
          echo "Building application..."
          sudo npm run build
          echo "Restarting nginx..."
          sudo systemctl restart nginx
          echo "Deployment completed successfully!"
        EOF
      env:
        SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}

    - name: Verify deployment
      run: |
        echo "Deployment completed successfully"